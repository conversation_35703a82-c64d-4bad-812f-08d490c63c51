"""
KyberSwap客户端
实现与KyberSwap聚合器API的交互，包括获取最佳交易路由和执行交易
"""

import json
import time
import asyncio
import requests
from typing import Dict, Any, Optional, List, Union
import yaml
from web3 import Web3
import os
from datetime import datetime, timedelta
import random

from .constants import SUPPORTED_CHAINS, API_CONFIG, TX_CONFIG

# 添加ETH价格缓存文件路径
ETH_PRICE_CACHE_FILE = os.path.join("cache", "eth_price_cache.json")

# 确保缓存目录存在
os.makedirs(os.path.dirname(ETH_PRICE_CACHE_FILE), exist_ok=True)

class KyberSwapClient:
    """
    KyberSwap API客户端，用于获取交易路径和执行代币交换
    """
    
    # KyberSwap路由合约地址
    ROUTER_ADDRESSES = {
        "ethereum": "******************************************",
        "polygon": "******************************************",
        "bsc": "******************************************",
        "base": "******************************************",
    }
    
    # API配置
    API_BASE_URL = "https://aggregator-api.kyberswap.com"
    API_VERSION = "v1"
    
    def __init__(self, chain: str, private_key: Optional[str] = None):
        """
        初始化KyberSwap客户端
        
        Args:
            chain: 区块链网络 (ethereum, polygon, bsc)
            private_key: 私钥 (可选，仅在执行交易时需要)
        """
        self.chain = chain.lower()
        self.private_key = private_key
        
        # 验证链是否支持
        if self.chain not in self.ROUTER_ADDRESSES:
            raise ValueError(f"不支持的链: {chain}")
            
        # 设置路由器地址
        self.router_address = self.ROUTER_ADDRESSES[self.chain]
        
        # 获取RPC URLs
        self.rpc_urls = self._get_rpc_urls()
        self.current_rpc_index = 0
        
        # 初始化代理配置
        self.proxy_enabled = False
        self.proxies = None
        self._load_proxy_config()
        
        # 初始化Web3
        self._init_web3()
            
        # 如果提供了私钥，设置账户
        if private_key:
            try:
                self.account = self.web3.eth.account.from_key(private_key)
                self.address = self.account.address
            except Exception as e:
                print(f"⚠️ 设置私钥时出错: {str(e)}")
                self.account = None
                self.address = None
        else:
            self.account = None
            self.address = None
            
        # 设置chain_config
        if self.chain in SUPPORTED_CHAINS:
            self.chain_config = SUPPORTED_CHAINS[self.chain]
        else:
            raise ValueError(f"在constants.py中找不到链配置: {self.chain}")
            
    def _init_web3(self):
        """初始化Web3连接"""
        # 如果是真实交易（有私钥），使用交易专用RPC，且不使用代理
        if self.private_key:
            trading_rpc_urls = self._get_trading_rpc_urls()
            if trading_rpc_urls:
                rpc_url = trading_rpc_urls[self.current_rpc_index]
                print(f"🔌 连接到交易专用RPC: {rpc_url}")

                # 真实交易时不使用代理，确保交易稳定性
                print("🔄 真实交易模式：不使用代理连接RPC")
                self.web3 = Web3(Web3.HTTPProvider(rpc_url))

                # 测试连接
                try:
                    self.web3.eth.chain_id
                    print(f"✅ 交易专用RPC连接成功")
                except Exception as e:
                    print(f"⚠️ 交易专用RPC连接失败: {str(e)}")
                    # 尝试下一个RPC
                    if self.current_rpc_index < len(trading_rpc_urls) - 1:
                        self.current_rpc_index += 1
                        print(f"🔄 尝试使用备用交易RPC...")
                        self._init_web3()
                return

        # 如果不是真实交易或没有找到交易专用RPC，使用默认RPC
        rpc_url = self.rpc_urls[self.current_rpc_index]
        print(f"🔌 连接到RPC: {rpc_url}")

        # 对于非交易操作，可以使用代理
        if self.proxy_enabled and self.proxies:
            proxy = self.proxies.get('http')
            if proxy:
                print(f"🔌 使用SOCKS5代理连接RPC: {proxy}")
                self.web3 = Web3(Web3.HTTPProvider(rpc_url, request_kwargs={'proxies': self.proxies}))
            else:
                self.web3 = Web3(Web3.HTTPProvider(rpc_url))
        else:
            self.web3 = Web3(Web3.HTTPProvider(rpc_url))

        # 测试连接
        try:
            self.web3.eth.chain_id
            print(f"✅ RPC连接成功")
        except Exception as e:
            print(f"⚠️ RPC连接失败: {str(e)}")
            # 尝试下一个RPC
            if self.current_rpc_index < len(self.rpc_urls) - 1:
                self.current_rpc_index += 1
                print(f"🔄 尝试使用备用RPC...")
                self._init_web3()
        
    def _get_rpc_urls(self) -> List[str]:
        """
        从配置文件获取RPC URL列表
        
        Returns:
            RPC URL列表
        """
        # 读取配置文件
        config_path = os.path.join("config", "config.yaml")
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
                
            # 获取RPC URL
            if self.chain in config["dex"]:
                chain_config = config["dex"][self.chain]
                urls = []
                
                # 添加主要RPC URL
                if "rpc_url" in chain_config:
                    urls.append(chain_config["rpc_url"])
                    # 添加备用RPC URLs
                    if "backup_rpc_urls" in chain_config:
                        urls.extend(chain_config["backup_rpc_urls"])
                elif "rpc" in chain_config:
                    if "endpoint" in chain_config["rpc"]:
                        urls.append(chain_config["rpc"]["endpoint"])
                    # 添加备用RPC URLs
                    if "backup_endpoints" in chain_config["rpc"]:
                        urls.extend(chain_config["rpc"]["backup_endpoints"])
                
                # 如果在配置中找到了有效的URLs，直接返回
                if urls:
                    print(f"✅ 从配置文件加载了 {len(urls)} 个RPC URLs")
                    return urls
            
            # 如果配置文件中没有找到有效的URLs，报错
            raise ValueError(f"配置文件中未找到链 {self.chain} 的RPC配置，请在config.yaml中添加相关配置")
            
        except Exception as e:
            print(f"❌ 读取配置文件失败: {str(e)}")
            raise ValueError(f"读取配置文件失败，请确保config/config.yaml存在并包含正确的RPC配置: {str(e)}")
    
    def _load_proxy_config(self):
        """从配置文件加载代理设置"""
        try:
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'config', 'config.yaml')
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                
            proxy_config = config.get('proxy', {})
            proxy_list = proxy_config.get('proxy_list', [])
            
            if proxy_list:
                # 随机选择一个代理
                proxy_url = random.choice(proxy_list)
                print(f"🔌 已启用SOCKS5代理: {proxy_url}")
                
                # 解析代理URL
                proxy_parts = proxy_url.replace('socks5://', '').split('@')
                if len(proxy_parts) == 2:
                    auth, host_port = proxy_parts
                    username, password = auth.split(':')
                    host, port = host_port.split(':')
                    
                    # 设置代理配置
                    self.proxies = {
                        'http': f'socks5h://{username}:{password}@{host}:{port}',
                        'https': f'socks5h://{username}:{password}@{host}:{port}'
                    }
                    self.proxy_enabled = True
                else:
                    print("⚠️ 代理URL格式不正确")
                    self.proxy_enabled = False
                    self.proxies = None
            else:
                print("ℹ️ 未找到代理配置")
                self.proxy_enabled = False
                self.proxies = None
                
        except Exception as e:
            print(f"⚠️ 加载代理配置时出错: {str(e)}")
            self.proxy_enabled = False
            self.proxies = None
    
            # 检查是否有代理列表
            if "proxy" in config and "proxy_list" in config["proxy"]:
                proxy_list = config["proxy"]["proxy_list"]
                if proxy_list:
                    # 随机选择一个代理
                    proxy = random.choice(proxy_list)
                    self.proxies = {
                        "http": proxy,
                        "https": proxy
                    }
                    self.proxy_enabled = True
                    print(f"🔌 已启用SOCKS5代理: {proxy}")
                else:
                    print("⚠️ 代理列表为空")
            else:
                print("⚠️ 未找到代理配置")
                
        except Exception as e:
            print(f"❌ 读取代理配置失败: {str(e)}")
            self.proxy_enabled = False
            self.proxies = None
    
    async def get_routes(self, token_in: str, token_out: str, amount_in: Union[str, float],
                        slippage: float = 0.5, is_native_in: bool = False,
                        save_gas: bool = False, excluded_sources: str = None,
                        is_wei_format: bool = True, use_proxy: bool = True) -> Dict[str, Any]:
        """
        获取代币交换路径 - 使用KyberSwap API v1

        Args:
            use_proxy: 是否使用代理，默认为True（报价时使用代理），False（真实交易时不使用代理）
        """
        try:
            # 转换金额为wei格式（如果需要）
            if not is_wei_format and not isinstance(amount_in, str):
                amount_in = self.convert_amount_to_wei(float(amount_in), token_in)
            elif not is_wei_format and isinstance(amount_in, str):
                try:
                    amount_in = self.convert_amount_to_wei(float(amount_in), token_in)
                except ValueError:
                    pass  # 如果无法转换，假设已经是wei格式的字符串
            
            # 检查输入金额是否有效
            if not amount_in or int(amount_in) <= 0:
                return {"error": "输入金额必须大于0"}
            
            # 构建API请求URL - 使用API_CONFIG中定义的v1版本endpoint
            api_url = f"{API_CONFIG['base_url']}{API_CONFIG['endpoints']['get_route'].format(chain=self.chain)}"
            
            # 获取接收方地址
            to_address = self.address if self.address else "******************************************"
            
            # 获取当前gas价格
            current_gas_price = self.web3.eth.gas_price
            
            # 处理原生代币输入
            if is_native_in:
                # 替换为符合KyberSwap要求的地址
                token_in = "******************************************"
            
            # 构建参数
            params = {
                "tokenIn": token_in,
                "tokenOut": token_out,
                "amountIn": amount_in,
                "gasInclude": "true",
                "gasPrice": str(current_gas_price),
                "saveGas": str(save_gas).lower() if save_gas else None,
            }
            
            # 如果指定了要排除的来源，添加到参数中
            if excluded_sources:
                params["excludedSources"] = excluded_sources
            
            print(f"API请求URL: {api_url}")
            print(f"请求参数: {json.dumps(params, indent=2)}")
            
            # 发送API请求
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "X-Client-Id": "kyberswap-api-client"
            }
            
            # 使用新的请求方法，支持自动重试和代理轮换
            try:
                response = await self._make_request_with_retry(
                    method='get',
                    url=api_url,
                    params=params,
                    headers=headers,
                    timeout=15,
                    use_proxy=use_proxy  # 根据传入参数决定是否使用代理
                )
                
                # 检查响应
                if response is None:
                    return {"error": "无法获取API响应，请检查网络连接"}
                    
                print(f"API响应状态码: {response.status_code}")
                # 截取响应内容，防止过长
                response_content = response.text[:500] + ("..." if len(response.text) > 500 else "")
                print(f"API响应内容: {response_content}")
                
                try:
                    routes_data = response.json()
                    
                    # 检查响应结构
                    print(f"响应数据结构: {list(routes_data.keys()) if isinstance(routes_data, dict) else type(routes_data)}")
                    
                    # 检查API v1响应格式
                    if "code" in routes_data and routes_data.get("code") == 0 and "data" in routes_data:
                        if "routeSummary" not in routes_data["data"]:
                            print("⚠️ API返回了成功状态码，但缺少routeSummary")
                            return {"error": "API响应缺少routeSummary字段"}
                    else:
                        error_message = routes_data.get("message", "未知错误")
                        return {"error": f"API响应格式不正确: {error_message}"}
                    
                    # 分析路由复杂度
                    route_summary = routes_data["data"]["routeSummary"]
                    route_complexity = 0
                    if "route" in route_summary:
                        for route_path in route_summary["route"]:
                            route_complexity += len(route_path)
                        print(f"⚠️ 路由复杂度: {route_complexity} 跳")
                    
                    # 确保路由地址正确
                    if "routerAddress" not in routes_data["data"]:
                        if self.chain in self.ROUTER_ADDRESSES:
                            routes_data["data"]["routerAddress"] = self.ROUTER_ADDRESSES[self.chain]
                        else:
                            print(f"⚠️ 无法确定路由器地址，使用默认地址")
                            routes_data["data"]["routerAddress"] = "******************************************"
                    
                    return routes_data
                    
                except json.JSONDecodeError as e:
                    print(f"⚠️ 解析API响应失败: {str(e)}")
                    return {"error": f"解析API响应失败: {str(e)}"}
                
            except Exception as e:
                print(f"⚠️ API请求失败: {str(e)}")
                return {"error": f"API请求失败: {str(e)}"}
            
        except Exception as e:
            print(f"⚠️ 获取路由时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return {"error": f"获取路由时出错: {str(e)}"}
    
    def _get_chain_id(self) -> int:
        """
        获取链ID
        
        Returns:
            链ID
        """
        chain_ids = {
            "ethereum": 1,
            "polygon": 137,
            "bsc": 56,
            "base": 8453
        }
        return chain_ids.get(self.chain, 1)  # 默认返回以太坊主网ID
    
    def get_token_decimals(self, token_address: str) -> int:
        """
        获取代币精度 (小数位数)
        
        Args:
            token_address: 代币地址
            
        Returns:
            代币精度 (小数位数)
        """
        # 文件路径
        decimals_file_path = os.path.join("data", "utils", "token", "gate_tokens_with_decimals.json")
        
        # 将chain转换为对应的网络名称
        network_map = {
            "ethereum": "ethereum",
            "polygon": "polygon",
            "bsc": "bsc",
            "base": "base"
        }
        network = network_map.get(self.chain.lower())
        token_address_lower = token_address.lower()
        
        # 尝试从gate_tokens_with_decimals.json读取代币精度
        try:
            # 判断文件是否存在
            if os.path.exists(decimals_file_path):
                with open(decimals_file_path, 'r', encoding='utf-8') as f:
                    tokens_data = json.load(f)
                    
                if network and network in tokens_data:
                    # 在对应网络中查找代币精度
                    for token_info in tokens_data[network]:
                        if "contract_address" in token_info and token_info["contract_address"].lower() == token_address_lower:
                            if "decimals" in token_info:
                                print(f"✅ 从gate_tokens_with_decimals.json读取到代币精度: {token_info['decimals']}")
                                return token_info["decimals"]
        except Exception as e:
            print(f"⚠️ 从文件读取代币精度时出错: {str(e)}")
        
        # 如果从文件中找不到，使用原来的方法
        
        # USDT等稳定币通常是6位精度
        common_stablecoins = {
            "polygon": {
                "******************************************": 6,  # USDT (Polygon)
                "******************************************": 6,  # USDC (Polygon)
                "******************************************": 18,  # DAI (Polygon)
            },
            "ethereum": {
                "******************************************": 6,  # USDT (Ethereum)
                "******************************************": 6,  # USDC (Ethereum)
            },
            "bsc": {
                "******************************************": 18,  # USDT (BSC)
                "******************************************": 18,  # USDC (BSC)
            },
            "base": {
                "******************************************": 6,  # USDC (Base)
                "******************************************": 18,  # DAI (Base)
                "******************************************": 6,  # USDbC (Base)
            }
        }
        
        # 如果是已知稳定币，直接返回对应精度
        if self.chain in common_stablecoins:
            if token_address_lower in common_stablecoins[self.chain]:
                decimals = common_stablecoins[self.chain][token_address_lower]
                # 更新到文件中
                self._update_token_decimals(token_address, decimals, network)
                return decimals
        
        # 对于原生代币和包装代币，返回18位精度
        if token_address.lower() == self.chain_config["native_token_address"].lower() or \
           token_address.lower() == self.chain_config["wrapped_token_address"].lower():
            # 更新到文件中
            self._update_token_decimals(token_address, 18, network)
            return 18
        
        try:
            # 创建代币合约实例
            token_contract = self.web3.eth.contract(
                address=self.web3.to_checksum_address(token_address),
                abi=[{
                    "constant": True,
                    "inputs": [],
                    "name": "decimals",
                    "outputs": [{"name": "", "type": "uint8"}],
                    "type": "function"
                }]
            )
            
            # 获取代币精度
            decimals = token_contract.functions.decimals().call()
            print(f"✅ 通过链上调用获取代币精度: {decimals}")
            
            # 更新到文件中
            self._update_token_decimals(token_address, decimals, network)
            
            return decimals
        except Exception as e:
            print(f"⚠️ 无法获取代币精度，使用默认值18: {str(e)}")
            # 使用默认精度18并更新到文件
            self._update_token_decimals(token_address, 18, network)
            return 18  # 默认精度
            
    def _update_token_decimals(self, token_address: str, decimals: int, network: str) -> None:
        """
        将获取到的代币精度更新到gate_tokens_with_decimals.json文件中
        
        Args:
            token_address: 代币地址
            decimals: 代币精度
            network: 网络名称 (ETH, MATIC, BSC)
        """
        if not network:
            print("⚠️ 无法确定网络名称，跳过更新代币精度")
            return
            
        # 文件路径
        decimals_file_path = os.path.join("data", "utils", "token", "gate_tokens_with_decimals.json")
        
        try:
            # 读取现有数据
            tokens_data = {}
            if os.path.exists(decimals_file_path):
                with open(decimals_file_path, 'r', encoding='utf-8') as f:
                    tokens_data = json.load(f)
            
            # 确保网络键存在
            if network not in tokens_data:
                tokens_data[network] = []
                
            # 检查代币是否已存在
            token_address_lower = token_address.lower()
            token_exists = False
            
            for token_info in tokens_data[network]:
                if "contract_address" in token_info and token_info["contract_address"].lower() == token_address_lower:
                    # 如果代币已存在但没有decimals字段或值不同，则更新
                    if "decimals" not in token_info or token_info["decimals"] != decimals:
                        token_info["decimals"] = decimals
                        print(f"💾 更新代币精度: {token_address} => {decimals}")
                    token_exists = True
                    break
            
            # 如果代币不存在，添加新记录
            if not token_exists:
                # 尝试获取代币符号
                symbol = self._get_token_symbol(token_address)
                new_token = {
                    "symbol": symbol if symbol else f"TOKEN_{token_address[:6]}",
                    "contract_address": token_address,
                    "decimals": decimals
                }
                tokens_data[network].append(new_token)
                print(f"💾 添加新代币精度: {token_address} => {decimals}")
                
            # 写回文件
            with open(decimals_file_path, 'w', encoding='utf-8') as f:
                json.dump(tokens_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"⚠️ 更新代币精度到文件时出错: {str(e)}")
            
    def _get_token_symbol(self, token_address: str) -> str:
        """
        获取代币符号
        
        Args:
            token_address: 代币地址
            
        Returns:
            str: 代币符号，如果获取失败则返回None
        """
        try:
            # 对于原生代币，返回链符号
            if token_address.lower() == self.chain_config["native_token_address"].lower():
                return self.chain_config["symbol"]
                
            # 对于包装代币，返回W+链符号
            if token_address.lower() == self.chain_config["wrapped_token_address"].lower():
                return f"W{self.chain_config['symbol']}"
            
            # 检查常用代币列表
            for symbol, addr in self.chain_config["common_tokens"].items():
                if addr.lower() == token_address.lower():
                    return symbol
            
            # 通过合约调用获取符号
            token_contract = self.web3.eth.contract(
                address=self.web3.to_checksum_address(token_address),
                abi=[{
                    "constant": True,
                    "inputs": [],
                    "name": "symbol",
                    "outputs": [{"name": "", "type": "string"}],
                    "type": "function"
                }]
            )
            
            # 获取符号
            symbol = token_contract.functions.symbol().call()
            return symbol
        except Exception as e:
            print(f"⚠️ 获取代币符号时出错: {str(e)}")
            return None

    def format_amount_with_decimals(self, amount: int, token_address: str) -> float:
        """
        根据代币精度格式化金额
        
        Args:
            amount: 以最小单位表示的金额
            token_address: 代币地址
            
        Returns:
            格式化后的金额
        """
        decimals = self.get_token_decimals(token_address)
        return amount / (10 ** decimals)

    async def swap(self, token_in: str, token_out: str, amount_in: str, slippage: float = 0.5,
                   is_native_in: bool = False, confirm: bool = True, save_gas: bool = False,
                   deadline_seconds: int = 60, simulate: bool = True, gas_multiplier: float = 3.0,
                   excluded_sources: str = None, recipient: str = None, expected_usdt_out: float = None) -> Dict[str, Any]:
        """
        执行代币交换

        Args:
            token_in: 输入代币地址
            token_out: 输出代币地址
            amount_in: 输入代币数量（以最小单位表示）
            slippage: 滑点容忍度（百分比，如0.5表示0.5%)
            is_native_in: 是否使用原生代币作为输入
            confirm: 是否等待交易确认
            save_gas: 是否优先考虑节省gas
            deadline_seconds: 交易超时时间（秒）
            simulate: 是否仅模拟交易
            gas_multiplier: Gas估计值乘数
            excluded_sources: 排除的流动性来源，逗号分隔
            recipient: 代币接收地址，默认为交易发送者地址
            expected_usdt_out: 预期的USDT输出量，用于卖出预验证（仅当token_out为USDT且为真实交易时生效）

        Returns:
            Dict: 包含交易结果的字典
        """
        try:
            # 如果是模拟交易，直接执行一次
            if simulate:
                return await self._execute_swap(
                    token_in, token_out, amount_in, slippage, is_native_in,
                    confirm, save_gas, deadline_seconds, simulate,
                    gas_multiplier, excluded_sources, recipient, expected_usdt_out
                )
            
            # 检查输入代币是否为USDT
            usdt_addresses = [
                "******************************************".lower(),  # Ethereum USDT
                "******************************************".lower()   # Polygon USDT
            ]
            token_in_lower = token_in.lower() if isinstance(token_in, str) else ""
            is_usdt_input = any(addr == token_in_lower for addr in usdt_addresses)

            # 根据输入代币类型决定重试次数
            if is_usdt_input:
                max_retries = 1  # USDT输入时不重试
                print("💰 检测到USDT输入，不进行重试")
            else:
                max_retries = 3  # 其他代币进行最多3次重试

            last_error = None

            for attempt in range(max_retries):
                try:
                    result = await self._execute_swap(
                        token_in, token_out, amount_in, slippage, is_native_in,
                        confirm, save_gas, deadline_seconds, simulate,
                        gas_multiplier, excluded_sources, recipient, expected_usdt_out
                    )
                    
                    # 如果交易成功，检查是否需要补充gas
                    if result["status"] == "成功":
                        # 检查当前交易是否是补充gas的交易
                        is_gas_swap = (token_in.lower() in [addr.lower() for addr in self.chain_config["common_tokens"].values() if "USDT" in addr] and
                                     token_out.lower() == self.chain_config["native_token_address"].lower())
                        
                        # 如果不是补充gas的交易，则检查是否需要补充gas
                        if not is_gas_swap:
                            await self._check_and_replenish_gas()
                        return result
                    
                    # 如果是明确的失败（而不是pending或未知状态），记录错误并继续重试
                    if result["status"] == "失败":
                        last_error = result.get("error", "未知错误")
                        print(f"第{attempt + 1}次尝试失败: {last_error}")
                        if attempt < max_retries - 1:
                            print(f"等待5秒后进行第{attempt + 2}次尝试...")
                            await asyncio.sleep(5)  # 在重试之前等待5秒
                        continue
                    
                    # 如果状态是pending或未知，直接返回结果
                    return result
                    
                except Exception as e:
                    last_error = str(e)
                    print(f"第{attempt + 1}次尝试发生异常: {last_error}")
                    if attempt < max_retries - 1:
                        print(f"等待5秒后进行第{attempt + 2}次尝试...")
                        await asyncio.sleep(5)
                    continue
            
            # 如果所有重试都失败，返回最后一次的错误
            return {
                "status": "失败",
                "error": f"在{max_retries}次尝试后仍然失败: {last_error}"
            }
            
        except Exception as e:
            return {
                "status": "失败",
                "error": str(e)
            }
            
    async def _execute_swap(self, token_in: str, token_out: str, amount_in: str, slippage: float = 0.5,
                         is_native_in: bool = False, confirm: bool = True, save_gas: bool = False,
                         deadline_seconds: int = 60, simulate: bool = True, gas_multiplier: float = 3.0,
                         excluded_sources: str = None, recipient: str = None, expected_usdt_out: float = None) -> Dict[str, Any]:
        """
        执行单次代币交换的内部方法
        """
        # 获取路由信息
        routes_data = await self.get_routes(
            token_in=token_in,
            token_out=token_out,
            amount_in=amount_in,
            slippage=slippage if simulate else 0.1,  # 真实交易时默认使用0.1%滑点
            is_native_in=is_native_in,
            save_gas=save_gas,
            excluded_sources=excluded_sources,
            use_proxy=simulate  # 模拟交易时使用代理，真实交易时不使用代理
        )
        
        # 仅当有私钥时才能执行实际交易
        if not simulate and not self.private_key:
            return {"status": "失败", "error": "未提供私钥，无法执行实际交易"}
            
        try:
            # 预定义最大重试次数
            MAX_RETRIES = 5
            current_retry = 0
            # 定义每次重试的滑点值 - 从低到高尝试5个滑点
            retry_slippages = [0.1, 0.3, 0.6, 1.0, 2.0]  # 0.1%, 0.3%, 0.6%, 1.0%, 2.0%
            current_slippage = retry_slippages[0] if not simulate else slippage
            current_save_gas = save_gas
            current_gas_multiplier = gas_multiplier
            
            # 获取以太坊地址
            if not self.address:
                return {"status": "失败", "error": "无法获取钱包地址，请检查私钥是否正确"}

            # 检查输入代币是否为USDT
            usdt_addresses = [
                "******************************************".lower(),  # Ethereum USDT
                "******************************************".lower()   # Polygon USDT
            ]
            token_in_lower = token_in.lower() if isinstance(token_in, str) else ""
            is_usdt_input = any(addr == token_in_lower for addr in usdt_addresses)

            # USDT输入时的特殊处理：路由获取可以重试，但交易执行失败后不重试
            if is_usdt_input and not simulate:
                print("💰 检测到USDT输入，路由获取正常重试，但交易执行失败后不重试")

            while current_retry < MAX_RETRIES:
                # 计算截止时间戳
                deadline = int(time.time()) + deadline_seconds

                # 标记是否已经成功构建过交易（用于USDT输入时的特殊处理）
                transaction_built_successfully = False
                
                # 获取交易路由
                print(f"\n🔍 获取交易路由... (当前滑点: {current_slippage}%)")
                routes_data = await self.get_routes(
                    token_in=token_in,
                    token_out=token_out,
                    amount_in=amount_in,
                    slippage=current_slippage,
                    is_native_in=is_native_in,
                    save_gas=current_save_gas,
                    excluded_sources=excluded_sources,
                    use_proxy=False  # 真实交易时不使用代理
                )
                
                if "error" in routes_data:
                    print(f"⚠️ 获取路由失败: {routes_data['error']}")
                    if current_retry < MAX_RETRIES - 1:
                        current_retry += 1
                        current_slippage = retry_slippages[current_retry]
                        current_save_gas = True
                        print(f"增加滑点至 {current_slippage}% 并启用gas优化，准备重试...")
                        continue
                    else:
                        return {"status": "失败", "error": f"在尝试所有滑点值后仍无法获取有效路由: {routes_data['error']}"}

                # 检查是否有路由数据
                if "data" not in routes_data or "routeSummary" not in routes_data["data"]:
                    print("⚠️ 找不到可用的交换路径")
                    if current_retry < MAX_RETRIES - 1:
                        current_retry += 1
                        current_slippage = retry_slippages[current_retry]
                        current_save_gas = True
                        print(f"增加滑点至 {current_slippage}% 并启用gas优化，准备重试...")
                        continue
                    else:
                        return {"status": "失败", "error": "在尝试所有滑点值后仍无法找到可用的交换路径"}

                # 分析路由复杂度
                route_summary = routes_data["data"]["routeSummary"]
                route_complexity = 0
                if "route" in route_summary:
                    for route_path in route_summary["route"]:
                        route_complexity += len(route_path)
                
                # 对于复杂路径，显示警告信息
                if route_complexity > 2:
                    print(f"⚠️ 检测到复杂路径 ({route_complexity} 跳)，这可能影响交易成功率")
                
                # 显示预计输出信息
                output_amount = int(route_summary["amountOut"])
                
                # 获取代币精度并格式化
                output_formatted = self.format_amount_with_decimals(output_amount, token_out)
                input_formatted = self.format_amount_with_decimals(int(amount_in), token_in)
                
                # 计算价格影响
                price_impact = 0
                if 'amountInUsd' in route_summary and 'amountOutUsd' in route_summary:
                    amount_in_usd = float(route_summary['amountInUsd'])
                    amount_out_usd = float(route_summary['amountOutUsd'])
                    if amount_in_usd > 0:
                        price_impact = (amount_in_usd - amount_out_usd) / amount_in_usd * 100
                
                # 显示交易预览
                print(f"\n📊 交易预览:")
                print(f"   输入: {input_formatted} {token_in}")
                print(f"   预计输出: {output_formatted} {token_out}")
                print(f"   价值: ${route_summary.get('amountInUsd', 'N/A')} → ${route_summary.get('amountOutUsd', 'N/A')}")
                print(f"   价格影响: {price_impact:.2f}%")
                print(f"   预估Gas: {route_summary.get('gas', 'N/A')}")
                
                # 显示路由合约地址
                router_address = routes_data["data"].get("routerAddress")
                if router_address:
                    print(f"   路由合约地址: {router_address}")
                else:
                    router_address = self.router_address
                    if router_address:
                        print(f"   路由合约地址: {router_address}")

                # 如果是模拟模式，返回模拟结果
                if simulate:
                    print("\n📝 准备交易数据...")
                    try:
                        tx_params = await self._build_swap_tx_params(
                            routes_data=routes_data,
                            deadline_seconds=deadline_seconds,
                            is_native_in=is_native_in,
                            receiver_address=recipient,
                            slippage=current_slippage  # 传递当前使用的滑点值
                        )
                        
                        # 构建更精确的state_override
                        state_override = {}
                        
                        # 1. 设置发送者ETH余额
                        state_override[self.address] = {
                            "balance": "0x" + "f" * 64,  # 最大uint256值
                            "nonce": hex(self.web3.eth.get_transaction_count(self.address)),
                            "code": "0x"
                        }

                        # 2. 如果不是原生代币，处理ERC20代币
                        if not is_native_in:
                            token_address = self.web3.to_checksum_address(token_in)
                            
                            # 获取代币合约代码
                            token_code = self.web3.eth.get_code(token_address)
                            
                            # 步骤1: 首先检查本地保存的槽位
                            cached_balance_slot, cached_allowance_slot = self._load_token_slots(token_address)
                            if cached_balance_slot is not None and cached_allowance_slot is not None:
                                print(f"\n🔍 使用本地缓存的槽位组合: balance({cached_balance_slot}), allowance({cached_allowance_slot})")
                                
                                # 使用缓存的槽位并支持重试
                                success, updated_override = await self._try_cached_slots(
                                    token_address,
                                    token_code,
                                    tx_params,
                                    state_override,
                                    cached_balance_slot,
                                    cached_allowance_slot
                                )
                                
                                if success:
                                    state_override = updated_override
                                else:
                                    return {
                                        "status": "模拟失败",
                                        "error": "本地缓存槽位验证失败（已重试3次）",
                                        "debug_info": {
                                            "token_address": token_address,
                                            "cached_slots": f"balance({cached_balance_slot}), allowance({cached_allowance_slot})"
                                        }
                                    }
                            else:
                                # 步骤2: 如果没有缓存，尝试默认的0/1槽位
                                try:
                                    print("\n🔍 尝试默认槽位(0/1)...")
                                    # 计算默认的存储槽
                                    balance_slot = Web3.solidity_keccak(
                                        ['uint256', 'uint256'],
                                        [int(self.address, 16), 0]  # slot 0 for balances mapping
                                    ).hex()
                                    
                                    allowance_base = Web3.solidity_keccak(
                                        ['uint256', 'uint256'],
                                        [int(self.address, 16), 1]  # slot 1 for allowances mapping
                                    ).hex()
                                    
                                    spender_slot = Web3.solidity_keccak(
                                        ['uint256', 'uint256'],
                                        [int(tx_params['to'], 16), int(allowance_base, 16)]
                                    ).hex()

                                    # 构建代币合约的状态覆盖
                                    token_state = {
                                        balance_slot: "0x" + "f" * 64,  # 最大余额
                                        spender_slot: "0x" + "f" * 64   # 最大授权
                                    }

                                    # 设置代币合约的状态覆盖
                                    state_override[token_address] = {
                                        "code": token_code.hex(),
                                        "balance": "0x0",
                                        "stateDiff": token_state
                                    }

                                    # 尝试执行模拟调用
                                    result = self.web3.eth.call(
                                        {
                                            "from": self.address,
                                            "to": tx_params['to'],
                                            "value": tx_params["value"],
                                            "data": tx_params["data"],
                                            "gas": 2000000
                                        },
                                        "latest",
                                        state_override
                                    )
                                    print("✅ 默认槽位(0/1)验证成功")
                                    # 保存成功的默认槽位
                                    self._save_token_slots(token_address, 0, 1)
                                    
                                except Exception as e:
                                    print(f"❌ 默认槽位验证失败: {str(e)}")
                                    print("开始尝试其他槽位组合...")
                                    
                                    # 步骤3: 如果默认槽位也失败，尝试其他槽位组合
                                    success, updated_override, slots = await self._try_all_storage_slots(
                                        token_address,
                                        tx_params,
                                        state_override
                                    )
                                    
                                    if success:
                                        state_override = updated_override
                                        print(f"✅ 找到有效的槽位组合: balance({slots[0]}), allowance({slots[1]})")
                                        # 保存成功的槽位组合
                                        self._save_token_slots(token_address, slots[0], slots[1])
                                    else:
                                        return {
                                            "status": "模拟失败",
                                            "error": "所有槽位组合都验证失败",
                                            "debug_info": {
                                                "token_address": token_address,
                                                "error": str(e)
                                            }
                                        }

                        try:
                            # 3. 添加路由合约的状态覆盖
                            router_address = tx_params['to']
                            router_code = self.web3.eth.get_code(router_address)
                            state_override[router_address] = {
                                "code": router_code.hex(),
                                "balance": "0x0"
                            }

                            print("\n🔍 执行交易模拟...")
                            
                            # 4. 使用eth_call进行模拟
                            call_result = self.web3.eth.call({
                                "from": tx_params["from"],
                                "to": tx_params["to"],
                                "value": tx_params["value"],
                                "data": tx_params["data"],
                                "gas": 2000000,
                                "gasPrice": self.web3.eth.gas_price
                            }, "latest", state_override)

                            print("✅ 交易模拟验证通过！")

                            # 5. 尝试估算实际gas（可选）
                            try:
                                estimated_gas = self.web3.eth.estimate_gas({
                                    "from": tx_params["from"],
                                    "to": tx_params["to"],
                                    "value": tx_params["value"],
                                    "data": tx_params["data"],
                                    "gasPrice": self.web3.eth.gas_price
                                }, "latest", state_override)
                                
                                print(f"💡 预估Gas: {estimated_gas}")
                                gas_limit = int(estimated_gas * 1.2)
                            except Exception as gas_e:
                                print(f"⚠️ Gas估算使用默认值: {str(gas_e)}")
                                gas_limit = 2000000

                            # 6. 解码输出（如果需要）
                            try:
                                # 这里可以添加输出解码逻辑
                                amounts_out = self.web3.codec.decode_abi(['uint256[]'], call_result)[0]
                                print(f"📊 预期输出金额: {amounts_out}")
                            except Exception as decode_e:
                                print(f"⚠️ 解码输出时出错: {str(decode_e)}")

                            return {
                                "status": "模拟成功",
                                "tx_hash": "0x..." + token_in[:6] + token_out[-6:],
                                "message": "交易模拟验证通过",
                                "gas_estimate": gas_limit,
                                "expected_output": amounts_out if 'amounts_out' in locals() else None
                            }

                        except Exception as e:
                            error_msg = str(e)
                            print(f"\n❌ 交易模拟失败: {error_msg}")
                            
                            if "TRANSFER_FROM_FAILED" in error_msg:
                                print("\n🔍 详细错误分析:")
                                print("1. 检查存储槽计算是否正确")
                                print(f"   - 余额槽位: {balance_slot if 'balance_slot' in locals() else 'N/A'}")
                                print(f"   - 授权槽位: {spender_slot if 'spender_slot' in locals() else 'N/A'}")
                                print("2. 验证合约代码是否正确保留")
                                print(f"   - 代币合约代码长度: {len(token_code.hex()) if 'token_code' in locals() else 'N/A'}")
                                print("3. 确认状态覆盖格式")
                                print(f"   - 状态覆盖结构: {json.dumps(state_override, indent=2)}")
                            
                            return {
                                "status": "模拟失败",
                                "error": error_msg,
                                "debug_info": {
                                    "token_address": token_address if 'token_address' in locals() else None,
                                    "balance_slot": balance_slot if 'balance_slot' in locals() else None,
                                    "spender_slot": spender_slot if 'spender_slot' in locals() else None
                                }
                            }

                    except Exception as e:
                        print(f"⚠️ 构建交易参数失败: {str(e)}")
                        return {
                            "status": "失败",
                            "error": f"构建交易参数失败: {str(e)}"
                        }
                    
                # 如果不是模拟模式，检查余额
                if not is_native_in:  # 检查ERC20代币余额
                    token_balance = self.get_balance(token_in)
                    amount_in_int = int(amount_in)
                    
                    # 计算0.1%的容差
                    tolerance = int(token_balance * 0.001)  # 0.1% = 0.001
                    
                    if amount_in_int > token_balance:
                        # 检查是否在容差范围内
                        if amount_in_int <= (token_balance + tolerance):
                            # 在容差范围内，使用实际余额
                            print(f"⚠️ 请求金额与可用余额相差小于0.1%，自动调整为使用全部余额")
                            amount_in = str(token_balance)
                        else:
                            # 超出容差范围，返回错误
                            return {
                                "status": "失败",
                                "error": f"代币余额不足。请求: {self.format_amount_with_decimals(amount_in_int, token_in)}，可用: {self.format_amount_with_decimals(token_balance, token_in)}"
                            }
                else:  # 检查原生代币余额
                    native_balance = self.web3.eth.get_balance(self.address)
                    if int(amount_in) > native_balance:
                        # 对于原生代币，返回错误而不是自动调整金额
                        return {
                            "status": "失败",
                            "error": f"原生代币余额不足。请求: {self.web3.from_wei(int(amount_in), 'ether')} {self.chain_config['symbol']}，可用: {self.web3.from_wei(native_balance, 'ether')} {self.chain_config['symbol']}"
                        }
                    elif int(amount_in) == native_balance:
                        # 预估gas成本
                        estimated_gas_cost = gas_limit * gas_price
                        # 至少保留gas成本的120%
                        min_reserve = int(estimated_gas_cost * 1.2)
                        
                        # 检查是否至少有足够的gas费
                        if native_balance <= min_reserve:
                            return {
                                "status": "失败",
                                "error": f"原生代币余额不足以支付交易gas费用。最低需要保留: {self.web3.from_wei(min_reserve, 'ether')} {self.chain_config['symbol']} 用于gas费"
                            }
                        
                        # 调整金额，保留足够的gas费
                        adjusted_amount = native_balance - min_reserve
                        amount_in = str(adjusted_amount)
                        input_formatted = self.format_amount_with_decimals(adjusted_amount, token_in)
                        print(f"调整后金额: {input_formatted} (保留约 {self.web3.from_wei(min_reserve, 'ether')} {self.chain_config['symbol']} 用于gas费)")
                
                # 检查代币授权（如果不是原生代币输入）
                if not is_native_in:
                    # 获取路由器地址
                    if "routerAddress" in routes_data["data"]:
                        router_address = routes_data["data"]["routerAddress"]
                    elif not router_address:
                        router_address = self.router_address
                        if not router_address:
                            return {"status": "失败", "error": "无法确定路由器合约地址"}
                    
                    # 检查授权情况
                    is_approved, _ = await self.check_token_approval(token_in, amount_in, router_address)
                    
                    if not is_approved:
                        print(f"⚠️ 代币尚未授权。")
                        print("请先批准代币授权")
                        return {"status": "失败", "error": "代币未授权，请先执行授权操作"}
                
                # 使用_build_swap_tx_params构建交易
                try:
                    # 构建交易参数
                    tx_params = await self._build_swap_tx_params(
                        routes_data=routes_data,
                        deadline_seconds=deadline_seconds,
                        is_native_in=is_native_in,
                        receiver_address=recipient,
                        slippage=current_slippage  # 传递当前使用的滑点值
                    )
                    
                    # 动态计算gas限制 - 使用预估Gas的乘数
                    route_summary = routes_data["data"]["routeSummary"]
                    estimated_gas = int(route_summary.get("gas", 200000))  # 默认值200000
                    min_gas_limit = 650000  # 最低gas限制底线
                    
                    # 根据路由复杂度和代币特性动态调整乘数
                    adjusted_multiplier = current_gas_multiplier
                    
                    # 获取token_in和token_out的小写地址用于比较
                    token_in_lower = token_in.lower() if isinstance(token_in, str) else ""
                    token_out_lower = token_out.lower() if isinstance(token_out, str) else ""
                    
                    # USDT地址
                    usdt_addresses = [
                        "******************************************".lower(),  # Ethereum USDT
                        "******************************************".lower()   # Polygon USDT
                    ]
                    
                    # 检查是否为USDT交易
                    is_usdt_involved = any(addr in token_in_lower or addr in token_out_lower for addr in usdt_addresses)
                    
                    # 分析路由复杂度
                    route_complexity = 0
                    if "route" in route_summary:
                        for route_path in route_summary["route"]:
                            route_complexity += len(route_path)
                    
                    # 调整乘数
                    if route_complexity > 2:
                        # 复杂路径需要更高的gas
                        adjusted_multiplier = max(adjusted_multiplier, 3.5)
                        print(f"路径复杂度: {route_complexity} 步骤, 增加gas乘数至 {adjusted_multiplier}")
                    elif is_usdt_involved:
                        # 任何涉及USDT的交易可能需要更多gas
                        adjusted_multiplier = max(adjusted_multiplier, 4.0)
                        print(f"USDT相关交易, 使用更高gas乘数: {adjusted_multiplier}")
                    
                    # 计算最终gas限制
                    gas_limit = max(int(estimated_gas * adjusted_multiplier), min_gas_limit)
                    
                    # 如果是重试的交易，增加gas限制
                    if current_retry > 0:
                        gas_limit = int(gas_limit * (1 + 0.2 * current_retry))  # 每次重试增加20%
                        print(f"第{current_retry}次重试, 增加gas限制至: {gas_limit}")
                    
                    # 准备交易参数 - 使用EIP-1559方式构建交易
                    # 检查网络是否支持EIP-1559
                    supports_eip1559 = self._supports_eip1559()

                    # 更新tx_params
                    tx_params['gas'] = gas_limit

                    if supports_eip1559:
                        # 使用EIP-1559交易类型
                        base_fee = self._get_base_fee()
                        max_priority_fee = self._calculate_priority_fee()
                        max_fee_per_gas = base_fee + max_priority_fee

                        # 设置EIP-1559参数
                        tx_params['type'] = 2  # EIP-1559交易类型
                        tx_params['maxFeePerGas'] = max_fee_per_gas
                        tx_params['maxPriorityFeePerGas'] = max_priority_fee

                        # 移除gasPrice（EIP-1559不使用）
                        if 'gasPrice' in tx_params:
                            del tx_params['gasPrice']

                        print(f"使用EIP-1559交易: baseFee={base_fee/10**9:.2f} Gwei, maxPriorityFee={max_priority_fee/10**9:.2f} Gwei, maxFeePerGas={max_fee_per_gas/10**9:.2f} Gwei")
                    else:
                        # 使用传统交易类型
                        base_gas_price = self.web3.eth.gas_price
                        gas_price = int(base_gas_price * 1.4)  # 使用1.4倍的gas价格
                        tx_params['gasPrice'] = gas_price
                        print(f"使用传统交易: gasPrice={gas_price/10**9:.2f} Gwei")
                    
                    # 打印详细的交易数据用于调试
                    print("\n📋 最终交易数据:")
                    print(f"   发送方: {self.address}")
                    print(f"   接收方 (路由器): {tx_params['to']}")
                    print(f"   接收代币地址: {recipient if recipient else self.address}")
                    print(f"   Value: {tx_params['value']}")
                    print(f"   Gas限制: {gas_limit} (预估: {estimated_gas}, 乘数: {adjusted_multiplier}倍)")

                    # 根据交易类型显示不同的gas费用信息
                    if supports_eip1559:
                        print(f"   EIP-1559交易类型:")
                        print(f"     - 基础费用: {base_fee / 10**9:.2f} Gwei")
                        print(f"     - 优先费用: {max_priority_fee / 10**9:.2f} Gwei")
                        print(f"     - 最大费用: {max_fee_per_gas / 10**9:.2f} Gwei")
                    else:
                        print(f"   传统交易类型:")
                        print(f"     - Gas价格: {tx_params['gasPrice'] / 10**9:.2f} Gwei")
                    
                    # 进行交易预验证
                    print("\n🔍 进行交易预验证...")
                    # 构建预验证参数（eth_call不需要gas费用参数）
                    call_params = {
                        "from": tx_params["from"],
                        "to": tx_params["to"],
                        "value": tx_params["value"],
                        "data": tx_params["data"],
                        "gas": tx_params["gas"]
                    }

                    # 使用eth_call模拟交易执行
                    call_result = self.web3.eth.call(call_params)
                    print(f"✅ 交易预验证通过!")

                    # 如果设置了预期USDT输出，进行输出值判断
                    if expected_usdt_out and expected_usdt_out > 0:
                        # 检查是否为卖出操作（输出代币为USDT）
                        is_sell_operation = self._is_usdt_output(token_out)

                        if is_sell_operation:
                            print(f"🔍 检测到卖出操作，开始验证USDT输出...")
                            print(f"   预期USDT输出: {expected_usdt_out}")
                            print(f"   最小可接受USDT: {expected_usdt_out - 1}")

                            # 解码交易结果获取实际USDT输出量
                            try:
                                # 解码返回的数据，通常是uint256[]格式
                                amounts_out = self.web3.codec.decode_abi(['uint256[]'], call_result)[0]
                                # 获取最后一个输出金额（通常是目标代币的数量）
                                raw_usdt_out = amounts_out[-1] if amounts_out else 0

                                # 获取USDT精度并格式化
                                usdt_decimals = self.get_token_decimals(token_out)
                                predicted_usdt_out = raw_usdt_out / (10 ** usdt_decimals)

                                print(f"📊 实际预测输出: {predicted_usdt_out} USDT")

                                # 检查预测输出是否满足条件
                                min_acceptable_usdt = expected_usdt_out - 1
                                if predicted_usdt_out < min_acceptable_usdt:
                                    error_msg = f"预测USDT输出不足: {predicted_usdt_out} < {min_acceptable_usdt} (预期{expected_usdt_out} - 1)"
                                    print(f"❌ {error_msg}")
                                    return {
                                        "status": "输出不足",
                                        "error": error_msg,
                                        "predicted_usdt_out": predicted_usdt_out,
                                        "min_acceptable_usdt": min_acceptable_usdt,
                                        "expected_usdt_out": expected_usdt_out
                                    }

                                print(f"✅ USDT输出验证通过: {predicted_usdt_out} >= {min_acceptable_usdt}")

                            except Exception as decode_e:
                                print(f"⚠️ 解码交易结果时出错: {str(decode_e)}")
                                # 如果解码失败，使用路由摘要中的数据作为备选
                                predicted_usdt_out = self._extract_usdt_output_from_route(route_summary, token_out)
                                print(f"📊 使用路由摘要预测输出: {predicted_usdt_out} USDT")

                                min_acceptable_usdt = expected_usdt_out - 1
                                if predicted_usdt_out < min_acceptable_usdt:
                                    error_msg = f"预测USDT输出不足: {predicted_usdt_out} < {min_acceptable_usdt} (预期{expected_usdt_out} - 1)"
                                    print(f"❌ {error_msg}")
                                    return {
                                        "status": "输出不足",
                                        "error": error_msg,
                                        "predicted_usdt_out": predicted_usdt_out,
                                        "min_acceptable_usdt": min_acceptable_usdt,
                                        "expected_usdt_out": expected_usdt_out
                                    }

                                print(f"✅ USDT输出验证通过: {predicted_usdt_out} >= {min_acceptable_usdt}")

                    # 标记交易已成功构建（用于USDT输入时的特殊处理）
                    transaction_built_successfully = True

                    # 用户确认交易 - 跳过确认，即使confirm为True也不要求用户确认
                    # 直接进行签名和发送交易
                    
                    # 签名并发送交易
                    try:
                        # 签名交易
                        signed_tx = self.web3.eth.account.sign_transaction(tx_params, self.private_key)
                        
                        # 发送交易
                        tx_hash = self.web3.eth.send_raw_transaction(signed_tx.rawTransaction)
                        tx_hash_hex = tx_hash.hex()
                        
                        print(f"✅ 成功: {tx_hash_hex}")
                        print(f"   查看交易: https://{self.chain}scan.com/tx/{tx_hash_hex}")
                        
                        # 等待交易确认
                        try:
                            print(f"⏳ 等待交易确认...")
                            tx_receipt = self.web3.eth.wait_for_transaction_receipt(tx_hash, timeout=180)
                            
                            if tx_receipt['status'] == 1:
                                print(f"✅ 交易已确认并成功执行")
                                
                                # 获取输出代币的代币符号（如果可能）
                                token_out_symbol = token_out
                                for symbol, address in SUPPORTED_CHAINS[self.chain]["common_tokens"].items():
                                    if address.lower() == token_out.lower():
                                        token_out_symbol = symbol
                                        break
                                        
                                # 计算输出金额
                                output_amount = int(route_summary.get("amountOut", 0))
                                token_out_decimals = self.get_token_decimals(token_out)
                                output_formatted = output_amount / (10 ** token_out_decimals)
                                
                                return {
                                    "status": "成功",
                                    "tx_hash": tx_hash_hex,
                                    "message": f"交换完成，接收了 {output_formatted} {token_out_symbol} 到地址 {recipient if recipient else self.address}",
                                    "receipt": tx_receipt,
                                    "route_summary": route_summary  # 添加路由摘要信息
                                }
                            else:
                                print(f"❌ 交易已确认但执行失败")
                                return {
                                    "status": "失败",
                                    "tx_hash": tx_hash_hex,
                                    "message": "交易已上链但执行失败",
                                    "receipt": tx_receipt
                                }
                        except Exception as e:
                            print(f"⚠️ 等待交易确认时出错: {str(e)}")
                            print(f"请在区块链浏览器中手动检查交易状态: https://{self.chain}scan.com/tx/{tx_hash_hex}")
                            return {
                                "status": "未知",
                                "tx_hash": tx_hash_hex,
                                "message": "交易已发送但确认状态未知，请手动检查"
                            }
                    
                    except Exception as e:
                        error_msg = str(e)
                        print(f"❌ 发送交易失败: {error_msg}")
                        
                        # 处理可能的错误并适当重试
                        if "nonce too low" in error_msg.lower():
                            print("尝试更新nonce并重试...")
                            tx_params['nonce'] = self.web3.eth.get_transaction_count(self.address, 'pending')

                            # 重新签名和发送（保持原有的交易类型）
                            signed_tx = self.web3.eth.account.sign_transaction(tx_params, self.private_key)
                            tx_hash = self.web3.eth.send_raw_transaction(signed_tx.rawTransaction)
                            tx_hash_hex = tx_hash.hex()
                            print(f"✅ 成功: {tx_hash_hex}")
                            print(f"   查看交易: https://{self.chain}scan.com/tx/{tx_hash_hex}")
                            
                            # 等待交易确认
                            try:
                                print(f"⏳ 等待交易确认...")
                                tx_receipt = self.web3.eth.wait_for_transaction_receipt(tx_hash, timeout=180)
                                
                                if tx_receipt['status'] == 1:
                                    print(f"✅ 交易已确认并成功执行")
                                    return {
                                        "status": "成功",
                                        "tx_hash": tx_hash_hex,
                                        "message": "交易已成功确认 (更新nonce后)",
                                        "receipt": tx_receipt
                                    }
                                else:
                                    print(f"❌ 交易已确认但执行失败")
                                    return {
                                        "status": "失败",
                                        "tx_hash": tx_hash_hex,
                                        "message": "交易已上链但执行失败 (更新nonce后)",
                                        "receipt": tx_receipt
                                    }
                            except Exception as e:
                                print(f"⚠️ 等待交易确认时出错: {str(e)}")
                                print(f"请在区块链浏览器中手动检查交易状态: https://{self.chain}scan.com/tx/{tx_hash_hex}")
                                return {
                                    "status": "未知",
                                    "tx_hash": tx_hash_hex,
                                    "message": "交易已发送但确认状态未知，请手动检查 (更新nonce后)"
                                }
                        
                        # 如果是gas不足的错误，增加gas并重试
                        if "gas" in error_msg.lower() and current_retry < MAX_RETRIES - 1:
                            print("疑似gas不足，增加gas限制并重试...")
                            current_gas_multiplier *= 1.5  # 增加50%的gas乘数
                            current_retry += 1
                            continue
                        
                        return {
                            "status": "失败",
                            "error": f"发送交易时出错: {str(e)}"
                        }
                
                except Exception as e:
                    error_msg = str(e)
                    print(f"❌ 构建或执行交易时出错: {error_msg}")
                    
                    # 特别处理常见错误
                    if "execution reverted" in error_msg or "路由摘要缺少必要参数" in error_msg:
                        print("\n⚠️ 交易构建失败，尝试调整参数...")

                        # USDT输入时：如果交易已成功构建但执行失败，不重试
                        if is_usdt_input and transaction_built_successfully:
                            print("💰 USDT输入模式：交易已构建成功但执行失败，不进行重试")
                            return {
                                "status": "失败",
                                "error": f"USDT交易执行失败: {error_msg}"
                            }

                        # 检查是否还有更多滑点可以尝试
                        if current_retry < MAX_RETRIES - 1:
                            current_retry += 1
                            current_slippage = retry_slippages[current_retry]
                            current_save_gas = True
                            current_gas_multiplier *= 1.5  # 增加50%的gas乘数
                            print(f"增加滑点至 {current_slippage}% 并启用gas优化，准备重试...")
                            continue
                        else:
                            print(f"已尝试所有滑点值: {retry_slippages}")
                            break

                    # 对于其他错误，也检查USDT输入模式（但只在交易已构建成功后才不重试）
                    if is_usdt_input and transaction_built_successfully:
                        print("💰 USDT输入模式：交易已构建成功但执行失败，不进行重试")
                        return {
                            "status": "失败",
                            "error": f"USDT交易执行失败: {error_msg}"
                        }

                    # 检查是否还有更多滑点可以尝试
                    if current_retry < MAX_RETRIES - 1:
                        current_retry += 1
                        current_slippage = retry_slippages[current_retry]
                        current_save_gas = True
                        current_gas_multiplier *= 1.5  # 增加50%的gas乘数
                        print(f"增加滑点至 {current_slippage}% 并启用gas优化，准备重试...")
                        continue
                    else:
                        print("\n⚠️ 所有滑点都已尝试失败，以下是可能的解决方法:")
                        print("1. 减少交易金额")
                        print("2. 稍后重试，等待市场条件改善")
                        print(f"3. 已尝试的滑点值: {retry_slippages}")

                        return {
                            "status": "失败",
                            "error": f"交易预验证失败: {error_msg}，已尝试所有滑点值 {retry_slippages}"
                        }
                    
            # 如果所有重试都失败
            return {
                "status": "失败",
                "error": f"已尝试所有滑点值 {retry_slippages}，但交易预验证始终失败"
            }
                
        except Exception as e:
            import traceback
            print(f"执行交易时出错: {str(e)}")
            print(traceback.format_exc())
            return {"status": "失败", "error": f"执行交易时出错: {str(e)}"}

    def get_balance(self, token_address: str) -> int:
        """
        获取代币余额
        
        Args:
            token_address: 代币地址
            
        Returns:
            代币余额 (以最小单位表示)
        """
        if not self.address:
            raise ValueError("未设置钱包地址，无法查询余额")
        
        try:
            # 检查是否为原生代币
            if token_address.lower() == self.chain_config["native_token_address"].lower():
                balance = self.web3.eth.get_balance(self.address)
                return balance
            
            # ERC20代币
            token_contract = self.web3.eth.contract(
                address=self.web3.to_checksum_address(token_address),
                abi=[{
                    "constant": True,
                    "inputs": [{"name": "_owner", "type": "address"}],
                    "name": "balanceOf",
                    "outputs": [{"name": "balance", "type": "uint256"}],
                    "type": "function"
                }]
            )
            
            balance = token_contract.functions.balanceOf(self.address).call()
            return balance
        except Exception as e:
            print(f"获取余额失败: {str(e)}")
            return 0

    async def check_token_approval(self, token_address: str, amount: str, router_address: Optional[str] = None) -> tuple:
        """
        检查代币授权并处理授权
        
        Args:
            token_address: 代币地址
            amount: 授权金额 (不再使用，总是进行无限授权)
            router_address: 路由器合约地址，如果不指定则使用默认地址
            
        Returns:
            tuple: (是否已授权, 交易哈希)
        """
        if not self.address or not self.private_key:
            raise ValueError("未设置钱包地址和私钥，无法检查授权")
        
        try:
            # 使用默认路由器地址
            if not router_address:
                router_address = self.router_address
            
            # 检查是否为原生代币，原生代币不需要授权
            if token_address.lower() == self.chain_config["native_token_address"].lower():
                return (True, None)
            
            # 创建ERC20合约实例
            erc20_abi = [
                {
                    "constant": True,
                    "inputs": [
                        {"name": "_owner", "type": "address"},
                        {"name": "_spender", "type": "address"}
                    ],
                    "name": "allowance",
                    "outputs": [{"name": "", "type": "uint256"}],
                    "type": "function"
                },
                {
                    "constant": False,
                    "inputs": [
                        {"name": "_spender", "type": "address"},
                        {"name": "_value", "type": "uint256"}
                    ],
                    "name": "approve",
                    "outputs": [{"name": "", "type": "bool"}],
                    "type": "function"
                }
            ]
            
            token_contract = self.web3.eth.contract(
                address=self.web3.to_checksum_address(token_address),
                abi=erc20_abi
            )
            
            # 获取当前授权
            try:
                current_allowance = token_contract.functions.allowance(
                    self.address,
                    self.web3.to_checksum_address(router_address)
                ).call()
            except Exception as e:
                print(f"获取授权失败: {str(e)}")
                return (False, None)
            
            # 使用无限授权 - 最大uint256值
            required_allowance = 2**256 - 1
            
            # 如果已经授权足够的金额
            if current_allowance >= int(amount):
                formatted_current = current_allowance / (10 ** self.get_token_decimals(token_address))
                formatted_required = int(amount) / (10 ** self.get_token_decimals(token_address))
                print(f"已授权足够的金额: {formatted_current} (需要: {formatted_required})")
                return (True, None)
            
            # 获取当前余额，检查是否需要调整授权金额
            token_balance = self.get_balance(token_address)
            amount_int = int(amount)
            
            # 计算0.1%的容差
            tolerance = int(token_balance * 0.001)  # 0.1% = 0.001
            
            # 如果请求金额超过余额但在容差范围内，使用实际余额
            if amount_int > token_balance and amount_int <= (token_balance + tolerance):
                print(f"⚠️ 授权金额与可用余额相差小于0.1%，自动调整为使用全部余额")
                amount = str(token_balance)
                amount_int = token_balance
            
            formatted_current = current_allowance / (10 ** self.get_token_decimals(token_address))
            formatted_required = amount_int / (10 ** self.get_token_decimals(token_address))
            print(f"授权不足。当前授权: {formatted_current}, 需要: {formatted_required}")
            print(f"将执行无限授权...")
            
            # 自动执行授权，不询问用户
            # 无限授权 - 使用最大uint256值
            max_amount = 2**256 - 1  # 最大uint256值
            
            # 构建授权交易 - 使用EIP-1559或传统方式
            base_tx_params = {
                'from': self.address,
                'gas': 100000,  # 固定gas值
                'nonce': self.web3.eth.get_transaction_count(self.address),
            }

            # 检查是否支持EIP-1559
            if self._supports_eip1559():
                # 使用EIP-1559交易类型
                base_fee = self._get_base_fee()
                max_priority_fee = self._calculate_priority_fee()
                max_fee_per_gas = base_fee + max_priority_fee

                base_tx_params.update({
                    'type': 2,  # EIP-1559交易类型
                    'maxFeePerGas': max_fee_per_gas,
                    'maxPriorityFeePerGas': max_priority_fee
                })

                print(f"授权交易使用EIP-1559: baseFee={base_fee/10**9:.2f} Gwei, maxPriorityFee={max_priority_fee/10**9:.2f} Gwei")
            else:
                # 使用传统交易类型
                base_tx_params['gasPrice'] = int(self.web3.eth.gas_price * 1.4)
                print(f"授权交易使用传统方式: gasPrice={base_tx_params['gasPrice']/10**9:.2f} Gwei")

            approve_txn = token_contract.functions.approve(
                self.web3.to_checksum_address(router_address),
                max_amount
            ).build_transaction(base_tx_params)
            
            # 签名交易
            signed_txn = self.web3.eth.account.sign_transaction(approve_txn, private_key=self.private_key)
            
            # 发送交易
            tx_hash = self.web3.eth.send_raw_transaction(signed_txn.rawTransaction)
            tx_hash_hex = tx_hash.hex()
            
            print(f"✨ 无限授权交易已发送: {tx_hash_hex}")
            
            # 等待交易确认
            try:
                tx_receipt = self.web3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)
                if tx_receipt['status'] == 1:
                    print("✅ 授权交易已确认")
                    return (True, tx_hash_hex)
                else:
                    print("❌ 授权交易失败")
                    return (False, tx_hash_hex)
            except Exception as e:
                print(f"等待交易确认时发生错误: {str(e)}")
                return (False, tx_hash_hex)
            
        except Exception as e:
            print(f"检查授权时发生错误: {str(e)}")
            return (False, None)

    async def wrap_native_token(self, amount: str, simulate: bool = True) -> Dict[str, Any]:
        """
        包装原生代币（例如：ETH -> WETH, MATIC -> WMATIC）
        
        Args:
            amount: 要包装的原生代币数量 (以wei为单位的字符串)
            simulate: 是否模拟交易
            
        Returns:
            交易结果字典
        """
        # 如果是模拟模式，则不需要检查私钥
        if not simulate:
            if not self.private_key:
                return {"status": "失败", "error": "未提供私钥，无法执行交易"}
            
            if not self.address:
                return {"status": "失败", "error": "未设置钱包地址，无法执行交易"}
            
        # 获取WETH合约地址
        wrapped_token_address = self.chain_config["wrapped_token_address"]
        
        # WETH ABI
        weth_abi = [
            {
                "constant": False,
                "inputs": [],
                "name": "deposit",
                "outputs": [],
                "payable": True,
                "stateMutability": "payable",
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [{"name": "", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "", "type": "uint256"}],
                "payable": False,
                "stateMutability": "view",
                "type": "function"
            }
        ]
        
        try:
            # 创建WETH合约实例
            weth_contract = self.web3.eth.contract(
                address=self.web3.to_checksum_address(wrapped_token_address),
                abi=weth_abi
            )
            
            # 检查是否模拟模式
            if simulate:
                print("\n⚠️ 模拟包装原生代币模式")
                print(f"将包装 {int(amount)/10**18} {self.chain_config['symbol']} 为 W{self.chain_config['symbol']}")
                return {
                    "status": "模拟成功", 
                    "tx_hash": "0x...wrap_" + str(int(amount))[-6:],
                    "message": f"这是一个模拟交易，没有在链上执行包装操作"
                }
                
            # 获取gas价格
            gas_price = self.web3.eth.gas_price
            
            # 预估gas
            gas_estimate = self.web3.eth.estimate_gas({
                'from': self.address,
                'to': wrapped_token_address,
                'value': int(amount),
                'data': weth_contract.encodeABI(fn_name="deposit")
            })
            
            # 构建交易 - 使用EIP-1559或传统方式
            tx = {
                'from': self.address,
                'to': wrapped_token_address,
                'value': int(amount),
                'gas': int(gas_estimate * 1.4),  # 增加40%的gas
                'nonce': self.web3.eth.get_transaction_count(self.address),
                'data': weth_contract.encodeABI(fn_name="deposit")
            }

            # 检查是否支持EIP-1559
            if self._supports_eip1559():
                # 使用EIP-1559交易类型
                base_fee = self._get_base_fee()
                max_priority_fee = self._calculate_priority_fee()
                max_fee_per_gas = base_fee + max_priority_fee

                tx.update({
                    'type': 2,  # EIP-1559交易类型
                    'maxFeePerGas': max_fee_per_gas,
                    'maxPriorityFeePerGas': max_priority_fee
                })

                print(f"包装交易使用EIP-1559: baseFee={base_fee/10**9:.2f} Gwei, maxPriorityFee={max_priority_fee/10**9:.2f} Gwei")
            else:
                # 使用传统交易类型
                tx['gasPrice'] = gas_price
                print(f"包装交易使用传统方式: gasPrice={gas_price/10**9:.2f} Gwei")
            
            # 签名交易
            signed_tx = self.web3.eth.account.sign_transaction(tx, private_key=self.private_key)
            
            # 发送交易
            tx_hash = self.web3.eth.send_raw_transaction(signed_tx.rawTransaction)
            tx_hash_hex = tx_hash.hex()
            
            print(f"包装交易已发送: {tx_hash_hex}")
            
            # 等待交易确认
            tx_receipt = self.web3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)
            
            if tx_receipt['status'] == 1:
                # 检查WETH余额是否增加
                weth_balance = weth_contract.functions.balanceOf(self.address).call()
                
                return {
                    "status": "成功",
                    "tx_hash": tx_hash_hex,
                    "message": f"成功将 {int(amount)/10**18} {self.chain_config['symbol']} 包装为 W{self.chain_config['symbol']}",
                    "wrapped_amount": amount,
                    "weth_balance": str(weth_balance)
                }
            else:
                return {
                    "status": "失败",
                    "tx_hash": tx_hash_hex,
                    "message": "包装交易执行失败"
                }
                
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            
            # 检查是否为RPC连接错误
            error_str = str(e).lower()
            if "max retries exceeded" in error_str or "connection" in error_str or "timeout" in error_str or "ssl" in error_str:
                # 尝试重新连接RPC
                if self.current_rpc_index < len(self.rpc_urls) - 1:
                    self.current_rpc_index += 1
                    print(f"🔄 RPC连接错误，尝试使用备用RPC...")
                    self._init_web3()
                    return {"status": "失败", "error": "RPC连接错误，已尝试切换到备用RPC，请重试交易"}
            
            return {"status": "失败", "error": f"执行包装操作时发生错误: {str(e)}\n{error_traceback}"}
            
    async def unwrap_native_token(self, amount: str, simulate: bool = True) -> Dict[str, Any]:
        """
        解包装代币（例如：WETH -> ETH, WMATIC -> MATIC）
        
        Args:
            amount: 要解包装的代币数量 (以wei为单位的字符串)
            simulate: 是否模拟交易
            
        Returns:
            交易结果字典
        """
        # 如果是模拟模式，则不需要检查私钥
        if not simulate:
            if not self.private_key:
                return {"status": "失败", "error": "未提供私钥，无法执行交易"}
                
            if not self.address:
                return {"status": "失败", "error": "未设置钱包地址，无法执行交易"}
            
        # 获取WETH合约地址
        wrapped_token_address = self.chain_config["wrapped_token_address"]
        
        # WETH ABI
        weth_abi = [
            {
                "constant": False,
                "inputs": [{"name": "wad", "type": "uint256"}],
                "name": "withdraw",
                "outputs": [],
                "payable": False,
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [{"name": "", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "", "type": "uint256"}],
                "payable": False,
                "stateMutability": "view",
                "type": "function"
            }
        ]
        
        try:
            # 创建WETH合约实例
            weth_contract = self.web3.eth.contract(
                address=self.web3.to_checksum_address(wrapped_token_address),
                abi=weth_abi
            )
            
            # 检查是否模拟模式
            if simulate:
                print("\n⚠️ 模拟解包装模式")
                print(f"将解包装 {int(amount)/10**18} W{self.chain_config['symbol']} 为 {self.chain_config['symbol']}")
                return {
                    "status": "模拟成功", 
                    "tx_hash": "0x...unwrap_" + str(int(amount))[-6:],
                    "message": f"这是一个模拟交易，没有在链上执行解包装操作"
                }
                
            # 检查WETH余额
            weth_balance = weth_contract.functions.balanceOf(self.address).call()
            if weth_balance < int(amount):
                return {"status": "失败", "error": f"余额不足，当前W{self.chain_config['symbol']}余额: {weth_balance}, 需要: {amount}"}
                
            # 构建交易 - 使用EIP-1559或传统方式
            base_tx_params = {
                'from': self.address,
                'gas': 100000,  # 预估gas
                'nonce': self.web3.eth.get_transaction_count(self.address),
            }

            # 检查是否支持EIP-1559
            if self._supports_eip1559():
                # 使用EIP-1559交易类型
                base_fee = self._get_base_fee()
                max_priority_fee = self._calculate_priority_fee()
                max_fee_per_gas = base_fee + max_priority_fee

                base_tx_params.update({
                    'type': 2,  # EIP-1559交易类型
                    'maxFeePerGas': max_fee_per_gas,
                    'maxPriorityFeePerGas': max_priority_fee
                })

                print(f"解包装交易使用EIP-1559: baseFee={base_fee/10**9:.2f} Gwei, maxPriorityFee={max_priority_fee/10**9:.2f} Gwei")
            else:
                # 使用传统交易类型
                base_tx_params['gasPrice'] = self.web3.eth.gas_price
                print(f"解包装交易使用传统方式: gasPrice={base_tx_params['gasPrice']/10**9:.2f} Gwei")

            tx = weth_contract.functions.withdraw(int(amount)).build_transaction(base_tx_params)
            
            # 签名交易
            signed_tx = self.web3.eth.account.sign_transaction(tx, private_key=self.private_key)
            
            # 发送交易
            tx_hash = self.web3.eth.send_raw_transaction(signed_tx.rawTransaction)
            tx_hash_hex = tx_hash.hex()
            
            print(f"解包装交易已发送: {tx_hash_hex}")
            
            # 等待交易确认
            tx_receipt = self.web3.eth.wait_for_transaction_receipt(tx_hash, timeout=120)
            
            if tx_receipt['status'] == 1:
                return {
                    "status": "成功",
                    "tx_hash": tx_hash_hex,
                    "message": f"成功将 {int(amount)/10**18} W{self.chain_config['symbol']} 解包装为 {self.chain_config['symbol']}",
                    "unwrapped_amount": amount
                }
            else:
                return {
                    "status": "失败",
                    "tx_hash": tx_hash_hex,
                    "message": "解包装交易执行失败"
                }
                
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            
            # 检查是否为RPC连接错误
            error_str = str(e).lower()
            if "max retries exceeded" in error_str or "connection" in error_str or "timeout" in error_str or "ssl" in error_str:
                # 尝试重新连接RPC
                if self.current_rpc_index < len(self.rpc_urls) - 1:
                    self.current_rpc_index += 1
                    print(f"🔄 RPC连接错误，尝试使用备用RPC...")
                    self._init_web3()
                    return {"status": "失败", "error": "RPC连接错误，已尝试切换到备用RPC，请重试交易"}
            
            return {"status": "失败", "error": f"执行解包装操作时发生错误: {str(e)}\n{error_traceback}"}

    async def encode_route(self, routes_data: Dict[str, Any], sender: str, recipient: str,
                        slippage_tolerance: float = 0.5, deadline: int = None,
                        source: str = "kyberswap-api-client", use_proxy: bool = False) -> Dict[str, Any]:
        """
        编码路由数据，为交易做准备 - 使用KyberSwap API v1
        
        Args:
            routes_data: 从get_routes获取的路由数据
            sender: 发送方地址
            recipient: 接收方地址
            slippage_tolerance: 滑点容忍度（百分比）
            deadline: 交易截止时间（Unix时间戳）
            source: 来源标识
            
        Returns:
            编码后的交易数据
        """
        try:
            # 检查路由数据是否有效
            if "data" not in routes_data or "routeSummary" not in routes_data["data"]:
                return {"error": "路由数据无效，缺少routeSummary"}
                
            # 获取routeSummary
            route_summary = routes_data["data"]["routeSummary"]
            
            # 设置默认截止时间（如果未提供）
            if deadline is None:
                deadline = int(time.time()) + 1200  # 默认20分钟后过期（按照文档建议）
            
            # 构建请求URL - 使用API_CONFIG中定义的v1版本endpoint
            api_url = f"{API_CONFIG['base_url']}{API_CONFIG['endpoints']['encode_route'].format(chain=self.chain)}"
            print(f"编码请求URL: {api_url}")
            print(f"使用滑点: {slippage_tolerance}%")
            
            # 确保收件人地址有效
            if not recipient or recipient == "******************************************":
                recipient = sender  # 使用发送方作为接收方

            # 准备请求正文 - 与APIv1格式一致
            request_body = {
                "routeSummary": route_summary,
                "sender": sender,
                "recipient": recipient,
                "slippageTolerance": int(slippage_tolerance * 100),  # 转换为基点 (bps)
                "deadline": deadline,
                "source": source
            }
            
            # 打印请求参数
            print(f"编码请求参数: {json.dumps({k: v for k, v in request_body.items() if k != 'routeSummary'}, indent=2)}")
            
            # 发送API请求
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "X-Client-Id": source
            }
            
            # 使用新的请求方法，根据use_proxy参数决定是否使用代理
            response = await self._make_request_with_retry(
                method='post',
                url=api_url,
                json=request_body,
                headers=headers,
                timeout=15,
                use_proxy=use_proxy
            )
            
            # 检查响应状态
            print(f"编码API响应状态码: {response.status_code}")
            
            if response.status_code != 200:
                try:
                    error_data = response.json()
                    error_code = error_data.get("code", 0)
                    error_message = error_data.get("message", "未知错误")
                    return {"error": f"编码API请求失败，错误码: {error_code}, 错误信息: {error_message}"}
                except:
                    return {"error": f"编码API请求失败，状态码: {response.status_code}, 响应: {response.text[:200]}"}
            
            # 解析响应
            try:
                encoded_data = response.json()
                print(f"API响应结构: {list(encoded_data.keys()) if isinstance(encoded_data, dict) else type(encoded_data)}")
                
                # 检查响应是否有效
                if "data" not in encoded_data or not encoded_data["data"]:
                    return {"error": "编码API返回无效数据"}
                
                # 检查是否有错误码
                if "code" in encoded_data and encoded_data["code"] != 0:
                    error_message = encoded_data.get("message", "未知错误")
                    return {"error": f"编码API返回错误: {error_message}"}
                
                # 按照 APIv1 规范处理响应数据
                result_data = encoded_data["data"]
                
                # 确保返回了必要的字段
                if "data" not in result_data and "callData" not in result_data:
                    return {"error": "编码API返回的数据缺少必要的交易数据"}
                    
                # 提取路由地址
                if "routerAddress" not in result_data:
                    if "routerAddress" in routes_data["data"]:
                        result_data["routerAddress"] = routes_data["data"]["routerAddress"]
                    elif self.chain in self.ROUTER_ADDRESSES:
                        result_data["routerAddress"] = self.ROUTER_ADDRESSES[self.chain]
                    else:
                        return {"error": "无法确定路由器地址"}
                
                return encoded_data
                
            except json.JSONDecodeError:
                return {"error": f"无法解析API响应: {response.text[:200]}"}
            
        except Exception as e:
            import traceback
            print(f"编码路由时出错: {str(e)}")
            print(traceback.format_exc())
            return {"error": f"编码路由时出错: {str(e)}"}

    def _get_gas_value(self, gas_data, default_value=500000):
        """
        安全地获取gas值，处理各种可能的数据类型
        
        Args:
            gas_data: 从API返回的gas数据
            default_value: 默认gas值
            
        Returns:
            int: 有效的gas整数值
        """
        if gas_data is None:
            return default_value
            
        if isinstance(gas_data, (int, float)):
            return int(gas_data)
            
        if isinstance(gas_data, str):
            try:
                return int(gas_data)
            except (ValueError, TypeError):
                return default_value
                
        if isinstance(gas_data, (list, tuple, dict)):
            return default_value
            
        # 对于其他类型，返回默认值
        return default_value

    async def get_eth_price_from_geckoterminal(self) -> float:
        """
        从GeckoTerminal API获取ETH价格，并将价格缓存到本地文件
        
        Returns:
            float: ETH价格(USD)，如果无法获取则返回默认值1800.0
        """
        # 检查缓存文件是否存在且有效
        cache_duration = 600  # 10分钟缓存时间
        current_time = int(time.time())
        
        # 尝试从缓存获取价格
        try:
            if os.path.exists(ETH_PRICE_CACHE_FILE):
                with open(ETH_PRICE_CACHE_FILE, 'r') as f:
                    cache_data = json.load(f)
                    cache_time = cache_data.get("timestamp", 0)
                    
                    # 检查缓存是否在有效期内
                    if current_time - cache_time < cache_duration:
                        price = cache_data.get("price", 0)
                        if price > 0:
                            print(f"使用文件缓存的ETH价格: ${price} (缓存时间: {datetime.fromtimestamp(cache_time).strftime('%Y-%m-%d %H:%M:%S')})")
                            return price
                
                print(f"ETH价格缓存已过期 (缓存时间: {datetime.fromtimestamp(cache_time).strftime('%Y-%m-%d %H:%M:%S')})")
        except Exception as e:
            print(f"读取ETH价格缓存文件出错: {str(e)}")
        
        # 如果缓存无效或不存在，从API获取价格
        eth_address = "******************************************"  # WETH地址
        api_endpoint = "https://api.geckoterminal.com/api/v2"
        url = f"{api_endpoint}/simple/networks/eth/token_price/{eth_address}"
        
        print(f"从GeckoTerminal获取ETH价格: {url}")
        
        # 设置请求头
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            "Accept": "application/json",
            "Referer": "https://www.geckoterminal.com/"
        }
        
        try:
            # 使用新的请求方法，获取价格信息可以使用代理
            response = await self._make_request_with_retry(
                method='get',
                url=url,
                headers=headers,
                timeout=10,
                use_proxy=True  # 获取价格信息可以使用代理
            )
            
            if response.status_code != 200:
                print(f"GeckoTerminal API请求失败，状态码: {response.status_code}")
                return 1800.0  # 如果无法获取，使用默认价格
            
            try:
                data = response.json()
                
                # 解析价格信息
                if ("data" in data and 
                    "attributes" in data["data"] and 
                    "token_prices" in data["data"]["attributes"]):
                    
                    token_prices = data["data"]["attributes"]["token_prices"]
                    eth_address_lower = eth_address.lower()
                    
                    # 尝试获取价格
                    for addr, price in token_prices.items():
                        if addr.lower() == eth_address_lower:
                            try:
                                price_float = float(price)
                                print(f"GeckoTerminal返回ETH价格: ${price_float}")
                                
                                # 将价格缓存到文件
                                cache_data = {
                                    "price": price_float,
                                    "timestamp": current_time,
                                    "datetime": datetime.fromtimestamp(current_time).strftime('%Y-%m-%d %H:%M:%S')
                                }
                                
                                try:
                                    # 确保缓存目录存在
                                    os.makedirs(os.path.dirname(ETH_PRICE_CACHE_FILE), exist_ok=True)
                                    with open(ETH_PRICE_CACHE_FILE, 'w') as f:
                                        json.dump(cache_data, f, indent=2)
                                    print(f"ETH价格已缓存到文件: {ETH_PRICE_CACHE_FILE}")
                                except Exception as e:
                                    print(f"缓存ETH价格到文件时出错: {str(e)}")
                                
                                return price_float
                            except (ValueError, TypeError) as e:
                                print(f"ETH价格转换失败: {price}, 错误: {str(e)}")
                                continue
            except json.JSONDecodeError as e:
                print(f"解析API响应失败: {str(e)}")
        except Exception as e:
            print(f"从GeckoTerminal获取ETH价格时出错: {str(e)}")
        
        # 如果所有尝试都失败，返回默认价格
        return 1800.0

    def _get_trading_rpc_urls(self) -> List[str]:
        """
        从config.yaml获取专用于交易的RPC URLs
        
        Returns:
            List[str]: RPC URL列表
        """
        try:
            # 读取配置文件
            config_path = os.path.join("config", "config.yaml")
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
                
            # 获取RPC配置
            if "rpc" in config and self.chain in config["rpc"]:
                rpc_config = config["rpc"][self.chain]
                urls = []
                
                # 添加主要RPC URL
                if "rpc_url" in rpc_config:
                    urls.append(rpc_config["rpc_url"])
                    
                # 添加备用RPC URLs
                if "backup_rpc_urls" in rpc_config and isinstance(rpc_config["backup_rpc_urls"], list):
                    urls.extend(rpc_config["backup_rpc_urls"])
                    
                if urls:
                    print(f"✅ 从config.yaml加载了 {len(urls)} 个交易专用RPC URLs")
                    return urls
                    
            print("⚠️ 在config.yaml中未找到交易专用RPC配置，将使用默认RPC")
            return []
            
        except Exception as e:
            print(f"⚠️ 读取交易专用RPC配置失败: {str(e)}")
            return []

    async def _build_swap_tx_params(
        self,
        routes_data: Dict,
        deadline_seconds: int = 60,
        is_native_in: bool = False,
        receiver_address: str = None,
        slippage: float = 0.5  # 添加slippage参数
    ) -> Dict:
        """
        构建交换交易的参数
        
        Args:
            routes_data: 路由数据
            deadline_seconds: 交易截止时间（秒）
            is_native_in: 是否为原生代币输入
            receiver_address: 代币接收地址，如果不指定则使用交易发送者地址
            slippage: 滑点容忍度（百分比）
        """
        # 获取交易专用RPC URLs
        trading_rpc_urls = self._get_trading_rpc_urls()
        current_trading_rpc_index = 0
        
        # 如果有交易专用RPC，创建新的Web3实例
        if trading_rpc_urls:
            trading_web3 = Web3(Web3.HTTPProvider(trading_rpc_urls[current_trading_rpc_index]))
            print(f"🔌 使用交易专用RPC: {trading_rpc_urls[current_trading_rpc_index]}")
        else:
            trading_web3 = self.web3
            print("⚠️ 使用默认RPC进行交易")
            
        try:
            # 初始化参数
            if not receiver_address:
                receiver_address = self.address if self.address else None
                
            # 验证接收地址
            if not receiver_address:
                raise ValueError("接收地址未指定，且未设置默认钱包地址")
                
            # 计算截止时间
            deadline = int(time.time() + deadline_seconds)
            
            # 获取路由器合约地址
            router_address = routes_data.get("routerAddress")
            if not router_address and "data" in routes_data:
                router_address = routes_data["data"].get("routerAddress")
                
            if not router_address:
                # 使用默认路由器地址
                if self.chain in self.ROUTER_ADDRESSES:
                    router_address = self.ROUTER_ADDRESSES[self.chain]
                else:
                    raise ValueError("无法确定路由器合约地址")
            
            print(f"使用路由器地址: {router_address}")
            print(f"接收地址: {receiver_address}")
            
            # 从路由数据中提取关键参数
            if "data" not in routes_data:
                raise ValueError("无效的路由数据，缺少data字段")
                
            route_data = routes_data["data"]
            
            # 获取routeSummary
            if "routeSummary" not in route_data:
                print(f"警告: 路由数据缺少routeSummary，尝试从完整路由数据中解析必要字段")
                # 尝试从其它字段解析
                route_summary = {}
            else:
                route_summary = route_data["routeSummary"]
                
            # 提取关键参数，打印详细信息便于调试
            print(f"路由摘要: {json.dumps(route_summary, indent=2)}")
            
            # 使用encode_route API来获取优化的交易参数
            print(f"使用encode_route API构建交易参数，滑点设置为: {slippage}%")
            encoded_data = await self.encode_route(
                routes_data=routes_data,
                sender=self.address,
                recipient=receiver_address,
                slippage_tolerance=slippage,  # 使用传入的滑点参数
                deadline=deadline,
                source="kyberswap-api-client",
                use_proxy=False  # 真实交易时不使用代理
            )
            
            if "error" in encoded_data:
                raise ValueError(f"编码路由失败: {encoded_data['error']}")
                
            # 从API响应中提取交易数据
            if "data" in encoded_data and isinstance(encoded_data["data"], dict):
                data_obj = encoded_data["data"]
                tx_data = None
                value = 0
                
                if "data" in data_obj:
                    tx_data = data_obj["data"]
                    if "transactionValue" in data_obj:
                        value = int(data_obj["transactionValue"])
                elif "callData" in data_obj:
                    tx_data = data_obj["callData"]
                    if "value" in data_obj:
                        value = int(data_obj["value"])
                
                if not tx_data:
                    raise ValueError("无法从API响应中提取交易数据")
                    
                # 构建最终交易
                tx_params = {
                    "from": self.address,
                    "to": router_address,
                    "value": value if is_native_in else 0,
                    "data": tx_data,
                    "nonce": trading_web3.eth.get_transaction_count(self.address),
                    "chainId": trading_web3.eth.chain_id,
                }
                return tx_params
            else:
                raise ValueError("API响应格式不正确或缺少必要字段")
                
        except Exception as e:
            # 如果是RPC错误，尝试切换到下一个交易专用RPC
            if trading_rpc_urls and ("connection" in str(e).lower() or "timeout" in str(e).lower()):
                current_trading_rpc_index = (current_trading_rpc_index + 1) % len(trading_rpc_urls)
                print(f"🔄 切换到下一个交易专用RPC: {trading_rpc_urls[current_trading_rpc_index]}")
                trading_web3 = Web3(Web3.HTTPProvider(trading_rpc_urls[current_trading_rpc_index]))
                # 重试一次
                return await self._build_swap_tx_params(
                    routes_data=routes_data,
                    deadline_seconds=deadline_seconds,
                    is_native_in=is_native_in,
                    receiver_address=receiver_address,
                    slippage=slippage
                )
            raise

    def _get_router_abi(self) -> List:
        """
        获取KyberSwap路由合约ABI
        
        Returns:
            List: 合约ABI
        """
        return [
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"},
                    {"internalType": "address[]", "name": "path", "type": "address[]"},
                    {"internalType": "address", "name": "to", "type": "address"},
                    {"internalType": "uint256", "name": "deadline", "type": "uint256"}
                ],
                "name": "swapExactETHForTokens",
                "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
                "stateMutability": "payable",
                "type": "function"
            },
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                    {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"},
                    {"internalType": "address[]", "name": "path", "type": "address[]"},
                    {"internalType": "address", "name": "to", "type": "address"},
                    {"internalType": "uint256", "name": "deadline", "type": "uint256"}
                ],
                "name": "swapExactTokensForTokens",
                "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
                "stateMutability": "nonpayable",
                "type": "function"
            }
        ]

    def convert_amount_to_wei(self, amount: float, token_address: str) -> str:
        """将人类可读的金额转换为wei格式的字符串"""
        decimals = self.get_token_decimals(token_address)
        return str(int(amount * (10 ** decimals)))

    def _supports_eip1559(self) -> bool:
        """
        检查当前网络是否支持EIP-1559

        Returns:
            bool: 是否支持EIP-1559
        """
        # 支持EIP-1559的网络
        eip1559_chains = {
            "ethereum": True,
            "polygon": True,
            "base": True,
            "bsc": False  # BSC不支持EIP-1559
        }

        return eip1559_chains.get(self.chain, False)

    def _get_base_fee(self) -> int:
        """
        获取当前区块的基础费用

        Returns:
            int: 基础费用 (wei)
        """
        try:
            # 对于Polygon等POA链，直接使用gas价格估算
            if self.chain == "polygon":
                current_gas_price = self.web3.eth.gas_price
                base_fee = int(current_gas_price * 0.7)  # Polygon的baseFee约为gasPrice的70%
                return base_fee

            # 对于其他链，尝试获取baseFeePerGas
            try:
                latest_block = self.web3.eth.get_block('latest')
                base_fee = latest_block.get('baseFeePerGas', 0)

                if base_fee > 0:
                    return base_fee
            except Exception as block_error:
                # 如果获取区块信息失败（如POA链），使用gas价格估算
                if "extraData" in str(block_error) or "POA" in str(block_error):
                    print(f"⚠️ 检测到POA链，使用gas价格估算基础费用")
                else:
                    print(f"⚠️ 获取区块信息失败: {str(block_error)}")

            # 如果无法获取baseFee，使用当前gas价格作为估算
            current_gas_price = self.web3.eth.gas_price
            base_fee = int(current_gas_price * 0.8)  # 估算baseFee约为gasPrice的80%
            return base_fee

        except Exception as e:
            print(f"⚠️ 获取基础费用失败: {str(e)}")
            # 使用当前gas价格作为fallback
            try:
                current_gas_price = self.web3.eth.gas_price
                return int(current_gas_price * 0.8)
            except Exception as gas_error:
                print(f"⚠️ 获取gas价格也失败: {str(gas_error)}")
                # 使用默认值
                return self.web3.to_wei(20, 'gwei')  # 默认20 Gwei

    def _calculate_priority_fee(self) -> int:
        """
        计算优先费用

        Returns:
            int: 优先费用 (wei)
        """
        try:
            # 不同网络的优先费用策略
            if self.chain == "ethereum":
                # 以太坊主网：使用硬编码的优先费用 0.1888 Gwei
                priority_fee = self.web3.to_wei(0.1888, 'gwei')  # 硬编码为 0.1888 Gwei
                print(f"使用硬编码的Ethereum优先费用: {0.1888} Gwei")
                return priority_fee

            elif self.chain == "polygon":
                # Polygon：使用较低的优先费用
                return self.web3.to_wei(288, 'gwei')  # 288 Gwei

            elif self.chain == "base":
                # Base：使用中等优先费用
                return self.web3.to_wei(0.1, 'gwei')  # 0.1 Gwei

            else:
                # 其他网络：使用默认值
                return self.web3.to_wei(1, 'gwei')  # 1 Gwei

        except Exception as e:
            print(f"⚠️ 计算优先费用失败: {str(e)}")
            # 使用默认优先费用
            return self.web3.to_wei(2, 'gwei')

    def _get_slots_file_path(self) -> str:
        """
        获取存储槽位文件的路径
        
        Returns:
            str: 文件路径
        """
        base_path = os.path.join("data", "utils", "token")
        file_name = "token_storage_slots.json"
        return os.path.join(base_path, file_name)

    def _load_token_slots(self, token_address: str) -> tuple:
        """
        从本地文件加载代币的存储槽位
        
        Args:
            token_address: 代币合约地址
            
        Returns:
            tuple: (balance_slot, allowance_slot) 如果找不到则返回 (None, None)
        """
        try:
            file_path = self._get_slots_file_path()
            
            # 如果文件不存在，直接返回None
            if not os.path.exists(file_path):
                print(f"⚠️ 槽位缓存文件不存在: {file_path}")
                return None, None
                
            # 如果文件存在但为空，返回None
            if os.path.getsize(file_path) == 0:
                print(f"⚠️ 槽位缓存文件为空: {file_path}")
                return None, None
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    slots_data = json.load(f)
            except json.JSONDecodeError:
                print(f"⚠️ 槽位缓存文件格式无效，将创建新文件")
                return None, None
                
            # 检查网络和代币地址
            network_data = slots_data.get(self.chain, {})
            token_data = network_data.get(token_address.lower(), {})
            
            if token_data:
                balance_slot = token_data.get('balance_slot')
                allowance_slot = token_data.get('allowance_slot')
                if balance_slot is not None and allowance_slot is not None:
                    print(f"✅ 从本地加载到代币 {token_address} 的存储槽位:")
                    print(f"   Balance槽位: {balance_slot}")
                    print(f"   Allowance槽位: {allowance_slot}")
                    return balance_slot, allowance_slot
                    
            return None, None
            
        except Exception as e:
            print(f"⚠️ 加载代币槽位时出错: {str(e)}")
            return None, None

    def _save_token_slots(self, token_address: str, balance_slot: int, allowance_slot: int, 
                         is_proxy: bool = False, implementation_address: str = None):
        """
        保存代币的存储槽位到本地文件，支持代理合约信息
        """
        try:
            file_path = self._get_slots_file_path()
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 读取现有数据
            slots_data = {}
            if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        slots_data = json.load(f)
                except (json.JSONDecodeError, UnicodeDecodeError):
                    print(f"⚠️ 现有槽位文件无效，将创建新文件")
                    slots_data = {}
            
            # 确保网络键存在
            if self.chain not in slots_data:
                slots_data[self.chain] = {}
                
            # 更新槽位数据
            token_address_lower = token_address.lower()
            slots_data[self.chain][token_address_lower] = {
                'balance_slot': balance_slot,
                'allowance_slot': allowance_slot,
                'is_proxy': is_proxy,
                'implementation_address': implementation_address.lower() if implementation_address else None,
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(slots_data, f, indent=2, ensure_ascii=False)
                
            print(f"✅ 已保存代币 {token_address} 的存储槽位到本地文件")
            if is_proxy:
                print(f"   代理合约: 是")
                print(f"   实现合约: {implementation_address}")
            print(f"   Balance槽位: {balance_slot}")
            print(f"   Allowance槽位: {allowance_slot}")
            
        except Exception as e:
            print(f"⚠️ 保存代币槽位时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())

    def _test_slot_combination(self, balance_slot_num: int, allowance_slot_num: int, 
                              token_address: str, token_code: bytes, tx_params: dict, 
                              state_override: dict) -> tuple:
        """
        测试特定的槽位组合是否有效
        
        Args:
            balance_slot_num: 余额槽位号
            allowance_slot_num: 授权槽位号
            token_address: 代币地址
            token_code: 代币合约代码
            tx_params: 交易参数
            state_override: 状态覆盖数据
            
        Returns:
            tuple: (是否成功, 状态覆盖数据, 槽位组合)
        """
        try:
            # 计算balance槽位
            balance_slot = Web3.solidity_keccak(
                ['uint256', 'uint256'],
                [int(self.address, 16), balance_slot_num]
            ).hex()
            
            # 计算allowance槽位
            allowance_base = Web3.solidity_keccak(
                ['uint256', 'uint256'],
                [int(self.address, 16), allowance_slot_num]
            ).hex()
            
            spender_slot = Web3.solidity_keccak(
                ['uint256', 'uint256'],
                [int(tx_params['to'], 16), int(allowance_base, 16)]
            ).hex()

            # 构建代币合约的状态覆盖
            token_state = {
                balance_slot: "0x" + "f" * 64,  # 最大余额
                spender_slot: "0x" + "f" * 64   # 最大授权
            }

            current_override = state_override.copy()
            current_override[token_address] = {
                "code": token_code.hex(),
                "balance": "0x0",
                "stateDiff": token_state
            }

            # 尝试执行模拟调用
            try:
                result = self.web3.eth.call(
                    {
                        "from": self.address,
                        "to": tx_params['to'],
                        "value": tx_params["value"],
                        "data": tx_params["data"],
                        "gas": 2000000
                    },
                    "latest",
                    current_override
                )
                return True, current_override, (balance_slot_num, allowance_slot_num)
            except Exception as call_error:
                error_msg = str(call_error).lower()
                # 检查是否是"Return amount is not enough"错误
                if "return amount is not enough" in error_msg:
                    print(f"✅ 找到正确的槽位组合 (balance: {balance_slot_num}, allowance: {allowance_slot_num})")
                    print("   错误'Return amount is not enough'表明槽位正确，只是返回金额不足")
                    # 这是正确的槽位组合，返回成功
                    return True, current_override, (balance_slot_num, allowance_slot_num)
                # 其他错误表示槽位组合无效
                return False, None, None
                
        except Exception as e:
            return False, None, None

    async def _try_all_storage_slots(self, token_address: str, tx_params: dict, state_override: dict) -> tuple:
        """
        尝试所有可能的存储槽位组合，包括代理合约支持
        """
        # 首先尝试直接分析合约
        token_code = self.web3.eth.get_code(token_address)
        max_slot = 20  # 测试前20个槽位
        
        # 首先尝试从本地加载槽位
        cached_balance_slot, cached_allowance_slot = self._load_token_slots(token_address)
        if cached_balance_slot is not None and cached_allowance_slot is not None:
            print(f"🔍 使用缓存的槽位组合: balance({cached_balance_slot}), allowance({cached_allowance_slot})")
            success, override, slots = self._test_slot_combination(
                cached_balance_slot,
                cached_allowance_slot,
                token_address,
                token_code,
                tx_params,
                state_override
            )
            if success:
                print("✅ 缓存的槽位组合验证成功")
                return True, override, (cached_balance_slot, cached_allowance_slot)
            else:
                print("⚠️ 缓存的槽位组合无效，尝试查找新的组合")

        # 1. 首先测试相邻的槽位组合
        print("\n🔍 测试相邻存储槽位组合...")
        for base_slot in range(max_slot - 1):
            # 测试 base_slot 和 base_slot+1
            print(f"测试相邻槽位组合: balance({base_slot}), allowance({base_slot+1})")
            success, override, slots = self._test_slot_combination(
                base_slot,
                base_slot + 1,
                token_address,
                token_code,
                tx_params,
                state_override
            )
            if success:
                print(f"✅ 找到有效的相邻槽位组合: balance({base_slot}), allowance({base_slot+1})")
                # 保存找到的槽位
                self._save_token_slots(token_address, base_slot, base_slot + 1)
                return True, override, (base_slot, base_slot + 1)
            
            # 测试反向组合
            print(f"测试相邻槽位组合: balance({base_slot+1}), allowance({base_slot})")
            success, override, slots = self._test_slot_combination(
                base_slot + 1,
                base_slot,
                token_address,
                token_code,
                tx_params,
                state_override
            )
            if success:
                print(f"✅ 找到有效的相邻槽位组合: balance({base_slot+1}), allowance({base_slot})")
                # 保存找到的槽位
                self._save_token_slots(token_address, base_slot + 1, base_slot)
                return True, override, (base_slot + 1, base_slot)

        # 2. 测试所有其他可能的组合
        print("\n🔍 测试所有其他槽位组合...")
        for balance_slot in range(max_slot):
            for allowance_slot in range(max_slot):
                if abs(balance_slot - allowance_slot) <= 1:
                    continue  # 跳过已经测试过的相邻组合
                
                print(f"测试槽位组合: balance({balance_slot}), allowance({allowance_slot})")
                success, override, slots = self._test_slot_combination(
                    balance_slot,
                    allowance_slot,
                    token_address,
                    token_code,
                    tx_params,
                    state_override
                )
                if success:
                    print(f"✅ 找到有效的槽位组合: balance({balance_slot}), allowance({allowance_slot})")
                    # 保存找到的槽位
                    self._save_token_slots(token_address, balance_slot, allowance_slot)
                    return True, override, (balance_slot, allowance_slot)

        # 如果直接分析失败，检查是否是代理合约
        if self._is_proxy_contract(token_address):
            print(f"\n🔍 检测到代理合约，尝试分析实现合约...")
            implementation_address = self._get_implementation_address(token_address)
            
            if implementation_address:
                print(f"找到实现合约地址: {implementation_address}")
                # 获取实现合约代码
                impl_code = self.web3.eth.get_code(implementation_address)
                
                # 尝试分析实现合约的槽位
                # 首先测试相邻的槽位组合
                print("\n🔍 测试实现合约的相邻存储槽位组合...")
                for base_slot in range(max_slot - 1):
                    # 测试 base_slot 和 base_slot+1
                    success, override, slots = self._test_slot_combination(
                        base_slot,
                        base_slot + 1,
                        implementation_address,
                        impl_code,
                        tx_params,
                        state_override
                    )
                    if success:
                        print(f"✅ 在实现合约中找到有效的槽位组合: balance({base_slot}), allowance({base_slot+1})")
                        # 保存槽位时同时记录是代理合约
                        self._save_token_slots(
                            token_address, 
                            base_slot,
                            base_slot + 1,
                            is_proxy=True,
                            implementation_address=implementation_address
                        )
                        return True, override, (base_slot, base_slot + 1)
                    
                    # 测试反向组合
                    success, override, slots = self._test_slot_combination(
                        base_slot + 1,
                        base_slot,
                        implementation_address,
                        impl_code,
                        tx_params,
                        state_override
                    )
                    if success:
                        print(f"✅ 在实现合约中找到有效的槽位组合: balance({base_slot+1}), allowance({base_slot})")
                        # 保存槽位时同时记录是代理合约
                        self._save_token_slots(
                            token_address, 
                            base_slot + 1,
                            base_slot,
                            is_proxy=True,
                            implementation_address=implementation_address
                        )
                        return True, override, (base_slot + 1, base_slot)

                # 测试所有其他可能的组合
                print("\n🔍 测试实现合约的其他槽位组合...")
                for balance_slot in range(max_slot):
                    for allowance_slot in range(max_slot):
                        if abs(balance_slot - allowance_slot) <= 1:
                            continue  # 跳过已经测试过的相邻组合
                        
                        success, override, slots = self._test_slot_combination(
                            balance_slot,
                            allowance_slot,
                            implementation_address,
                            impl_code,
                            tx_params,
                            state_override
                        )
                        if success:
                            print(f"✅ 在实现合约中找到有效的槽位组合: balance({balance_slot}), allowance({allowance_slot})")
                            # 保存槽位时同时记录是代理合约
                            self._save_token_slots(
                                token_address, 
                                balance_slot,
                                allowance_slot,
                                is_proxy=True,
                                implementation_address=implementation_address
                            )
                            return True, override, (balance_slot, allowance_slot)
                    
        print("❌ 所有槽位分析尝试都失败")
        return False, None, None

    async def _try_cached_slots(self, token_address: str, token_code: bytes, tx_params: dict, 
                               state_override: dict, balance_slot: int, allowance_slot: int,
                               max_retries: int = 3) -> tuple:
        """
        尝试使用缓存的槽位，支持多次重试
        """
        for retry in range(max_retries):
            try:
                if retry > 0:
                    print(f"\n🔄 第 {retry + 1} 次重试使用缓存槽位...")
                
                # 计算槽位
                balance_slot_hash = Web3.solidity_keccak(
                    ['uint256', 'uint256'],
                    [int(self.address, 16), balance_slot]
                ).hex()
                
                allowance_base = Web3.solidity_keccak(
                    ['uint256', 'uint256'],
                    [int(self.address, 16), allowance_slot]
                ).hex()
                
                spender_slot = Web3.solidity_keccak(
                    ['uint256', 'uint256'],
                    [int(tx_params['to'], 16), int(allowance_base, 16)]
                ).hex()

                # 构建代币合约的状态覆盖
                token_state = {
                    balance_slot_hash: "0x" + "f" * 64,  # 最大余额
                    spender_slot: "0x" + "f" * 64   # 最大授权
                }

                current_override = state_override.copy()
                current_override[token_address] = {
                    "code": token_code.hex(),
                    "balance": "0x0",
                    "stateDiff": token_state
                }

                # 尝试执行模拟调用
                try:
                    result = self.web3.eth.call(
                        {
                            "from": self.address,
                            "to": tx_params['to'],
                            "value": tx_params["value"],
                            "data": tx_params["data"],
                            "gas": 2000000
                        },
                        "latest",
                        current_override
                    )
                    print(f"✅ 本地缓存槽位验证成功 (尝试次数: {retry + 1})")
                    return True, current_override
                except Exception as call_error:
                    error_msg = str(call_error).lower()
                    # 检查是否是"Return amount is not enough"错误
                    if "return amount is not enough" in error_msg:
                        print(f"✅ 本地缓存槽位验证成功 (尝试次数: {retry + 1})")
                        print("   错误'Return amount is not enough'表明槽位正确，只是返回金额不足")
                        return True, current_override
                    raise  # 重新抛出其他错误
                    
            except Exception as e:
                print(f"❌ 本地缓存槽位验证失败 (尝试次数: {retry + 1}): {str(e)}")
                if retry < max_retries - 1:
                    await asyncio.sleep(1)  # 在重试之间添加短暂延迟
                    continue
                
        return False, None

    def _is_proxy_contract(self, address: str) -> bool:
        """
        检查合约是否是代理合约
        """
        try:
            # 常见的代理合约方法
            proxy_signatures = [
                "0x5c60da1b",  # implementation()
                "0xb53127684a568b3173ae13b9f8a6016e243e63b6e8ee1178d6a717850b5d6103",  # admin()
                "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc",  # implementation slot
            ]
            
            # 获取合约代码
            code = self.web3.eth.get_code(address).hex()
            
            # 检查代码中是否包含代理合约特征
            for sig in proxy_signatures:
                if sig[2:] in code:  # 移除'0x'前缀进行比较
                    return True
                    
            return False
        except Exception as e:
            print(f"⚠️ 检查代理合约时出错: {str(e)}")
            return False

    def _get_implementation_address(self, proxy_address: str) -> Optional[str]:
        """
        获取代理合约的实现合约地址
        """
        try:
            # 1. 尝试调用implementation()方法
            implementation_abi = [{"inputs":[],"name":"implementation","outputs":[{"internalType":"address","type":"address"}],"stateMutability":"view","type":"function"}]
            proxy_contract = self.web3.eth.contract(address=proxy_address, abi=implementation_abi)
            
            try:
                impl_address = proxy_contract.functions.implementation().call()
                if impl_address and impl_address != "******************************************":
                    print(f"✅ 通过implementation()方法获取到实现合约地址: {impl_address}")
                    return impl_address
            except Exception as e:
                print(f"通过implementation()方法获取实现地址失败: {str(e)}")
            
            # 2. 尝试从存储槽读取实现地址
            implementation_slot = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc"
            impl_address = self.web3.eth.get_storage_at(proxy_address, implementation_slot)
            
            # 转换为地址格式
            impl_address = "0x" + impl_address.hex()[-40:]
            if impl_address and impl_address != "******************************************":
                print(f"✅ 通过存储槽获取到实现合约地址: {impl_address}")
                return impl_address
                
            return None
        except Exception as e:
            print(f"⚠️ 获取实现合约地址时出错: {str(e)}")
            return None

    async def _try_direct_storage_slots(self, token_address: str, tx_params: dict, state_override: dict) -> tuple:
        """
        直接尝试分析合约的存储槽位（原有的分析逻辑）
        """
        token_code = self.web3.eth.get_code(token_address)
        max_slot = 20  # 测试前20个槽位
        
        # 首先尝试从本地加载槽位
        cached_balance_slot, cached_allowance_slot = self._load_token_slots(token_address)
        if cached_balance_slot is not None and cached_allowance_slot is not None:
            print(f"🔍 使用缓存的槽位组合: balance({cached_balance_slot}), allowance({cached_allowance_slot})")
            success, override, slots = self._test_slot_combination(
                cached_balance_slot,
                cached_allowance_slot,
                token_address,
                token_code,
                tx_params,
                state_override
            )
            if success:
                print("✅ 缓存的槽位组合验证成功")
                return True, override, slots
            else:
                print("⚠️ 缓存的槽位组合无效，尝试查找新的组合")

        # 1. 首先测试相邻的槽位组合
        print("\n🔍 测试相邻存储槽位组合...")
        for base_slot in range(max_slot - 1):
            # 测试 base_slot 和 base_slot+1
            print(f"测试相邻槽位组合: balance({base_slot}), allowance({base_slot+1})")
            success, override, slots = self._test_slot_combination(
                base_slot,
                base_slot + 1,
                token_address,
                token_code,
                tx_params,
                state_override
            )
            if success:
                print(f"✅ 找到有效的相邻槽位组合: balance({base_slot}), allowance({base_slot+1})")
                # 保存找到的槽位
                self._save_token_slots(token_address, base_slot, base_slot + 1)
                return True, override, slots
            
            # 测试反向组合
            print(f"测试相邻槽位组合: balance({base_slot+1}), allowance({base_slot})")
            success, override, slots = self._test_slot_combination(
                base_slot + 1,
                base_slot,
                token_address,
                token_code,
                tx_params,
                state_override
            )
            if success:
                print(f"✅ 找到有效的相邻槽位组合: balance({base_slot+1}), allowance({base_slot})")
                # 保存找到的槽位
                self._save_token_slots(token_address, base_slot + 1, base_slot)
                return True, override, slots

        # 2. 测试所有其他可能的组合
        print("\n🔍 测试所有其他槽位组合...")
        for balance_slot in range(max_slot):
            for allowance_slot in range(max_slot):
                if abs(balance_slot - allowance_slot) <= 1:
                    continue  # 跳过已经测试过的相邻组合
                
                print(f"测试槽位组合: balance({balance_slot}), allowance({allowance_slot})")
                success, override, slots = self._test_slot_combination(
                    balance_slot,
                    allowance_slot,
                    token_address,
                    token_code,
                    tx_params,
                    state_override
                )
                if success:
                    print(f"✅ 找到有效的槽位组合: balance({balance_slot}), allowance({allowance_slot})")
                    # 保存找到的槽位
                    self._save_token_slots(token_address, balance_slot, allowance_slot)
                    return True, override, slots

        return False, None, None

    def rotate_proxies(self):
        """
        轮换代理设置
        """
        try:
            # 读取配置文件
            config_path = os.path.join("config", "config.yaml")
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
                
            # 检查是否有代理列表配置
            if "proxy" not in config or "proxy_list" not in config["proxy"]:
                print("⚠️ 配置文件中没有代理列表")
                return False
                
            proxy_list = config["proxy"]["proxy_list"]
            if not proxy_list:
                print("⚠️ 代理列表为空")
                return False
                
            # 获取当前代理索引
            current_index = getattr(self, "current_proxy_index", -1)
            # 计算下一个代理索引
            next_index = (current_index + 1) % len(proxy_list)
            new_proxy = proxy_list[next_index]
            
            # 更新代理配置
            self.proxies = {
                "http": new_proxy,
                "https": new_proxy
            }
            
            # 保存当前索引
            self.current_proxy_index = next_index
            
            print(f"✅ 已轮换到新代理 [{next_index + 1}/{len(proxy_list)}]: {new_proxy}")
            return True
            
        except Exception as e:
            print(f"⚠️ 轮换代理失败: {str(e)}")
            return False

    def _is_rate_limit_error(self, response_or_error) -> bool:
        """
        检查是否是速率限制错误
        
        Args:
            response_or_error: 响应对象或错误信息
            
        Returns:
            bool: 是否是速率限制错误
        """
        try:
            # 检查HTTP响应状态码
            if hasattr(response_or_error, 'status_code'):
                if response_or_error.status_code == 429:
                    return True
                    
                # 检查响应文本
                try:
                    response_text = response_or_error.text.lower()
                    if "rate limit" in response_text or "too many requests" in response_text:
                        return True
                        
                    # 尝试解析JSON响应
                    response_json = response_or_error.json()
                    if isinstance(response_json, dict):
                        # 检查错误码 -32090
                        if response_json.get('code') == -32090:
                            return True
                        # 检查错误信息
                        error_msg = str(response_json.get('message', '')).lower()
                        if "rate limit" in error_msg or "too many requests" in error_msg:
                            return True
                except:
                    pass
                    
            # 检查异常信息
            elif isinstance(response_or_error, Exception):
                error_msg = str(response_or_error).lower()
                return ("rate limit" in error_msg or 
                        "too many requests" in error_msg or 
                        "429" in error_msg or 
                        "-32090" in error_msg)
                        
            # 检查字符串
            elif isinstance(response_or_error, str):
                error_msg = response_or_error.lower()
                return ("rate limit" in error_msg or 
                        "too many requests" in error_msg or 
                        "429" in error_msg or 
                        "-32090" in error_msg)
                        
        except Exception as e:
            print(f"⚠️ 检查速率限制时出错: {str(e)}")
            
        return False

    async def _make_request_with_retry(self, method: str, url: str, max_retries: int = 3,
                                     retry_delay: int = 2, use_proxy: bool = True, **kwargs) -> requests.Response:
        """
        发送HTTP请求，支持重试和代理

        Args:
            method: HTTP方法 (GET, POST等)
            url: 请求URL
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            use_proxy: 是否使用代理，默认为True
            **kwargs: 其他请求参数

        Returns:
            requests.Response对象
        """
        # 设置默认超时
        if 'timeout' not in kwargs:
            kwargs['timeout'] = 30  # 增加超时时间到30秒

        # 根据use_proxy参数决定是否使用代理
        if use_proxy and self.proxy_enabled and self.proxies:
            kwargs['proxies'] = self.proxies
            print(f"🔌 使用代理发送请求: {self.proxies}")
        elif not use_proxy:
            print("🔄 不使用代理发送请求")
            # 确保不使用代理
            kwargs.pop('proxies', None)

        last_error = None
        for attempt in range(max_retries):
            try:
                print(f"API请求URL: {url}")
                if 'json' in kwargs:
                    print(f"请求参数: {json.dumps(kwargs['json'], indent=2)}")

                response = requests.request(method, url, **kwargs)

                # 检查响应状态
                if response.status_code == 200:
                    return response
                elif self._is_rate_limit_error(response):
                    if attempt < max_retries - 1:
                        wait_time = retry_delay * (attempt + 1)
                        print(f"⚠️ 遇到速率限制，等待 {wait_time} 秒后重试...")
                        await asyncio.sleep(wait_time)
                        continue
                else:
                    error_msg = f"API请求失败: {response.status_code} - {response.text}"
                    print(f"⚠️ {error_msg}")
                    last_error = error_msg

            except (requests.exceptions.RequestException, Exception) as e:
                error_msg = f"API请求失败: {str(e)}"
                print(f"⚠️ {error_msg}")
                last_error = error_msg
                if attempt < max_retries - 1:
                    wait_time = retry_delay * (attempt + 1)
                    print(f"🔄 等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                    continue

        # 所有重试都失败
        if last_error:
            raise Exception(last_error)
        else:
            raise Exception("API请求失败: 未知错误")

    def rotate_proxies(self):
        """轮换代理"""
        if not self.proxy_enabled:
            return
            
        config_path = os.path.join("config", "config.yaml")
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
                
            if "proxy" in config and "proxy_list" in config["proxy"]:
                proxy_list = config["proxy"]["proxy_list"]
                if proxy_list:
                    # 随机选择一个新代理
                    proxy = random.choice(proxy_list)
                    self.proxies = {
                        "http": proxy,
                        "https": proxy
                    }
                    print(f"🔄 已切换到新代理: {proxy}")
                    
        except Exception as e:
            print(f"❌ 轮换代理失败: {str(e)}")

    async def _check_and_replenish_gas(self) -> None:
        """
        检查gas余额并在需要时补充。这个方法在每次交易成功后调用。
        """
        try:
            # 获取当前原生代币余额
            native_balance = self.web3.eth.get_balance(self.address)
            native_balance_in_eth = self.web3.from_wei(native_balance, 'ether')
            
            # 根据不同链设置阈值和兑换金额
            if self.chain == "ethereum":
                threshold = 0.01  # 0.01 ETH
                usdt_amount = "50"  # 50 USDT
            elif self.chain == "polygon":
                threshold = 1  # 1 MATIC
                usdt_amount = "1"  # 1 USDT
            else:
                # 其他链不执行自动兑换
                return
                
            print(f"\n💰 检查{self.chain_config['symbol']}余额...")
            print(f"当前余额: {native_balance_in_eth} {self.chain_config['symbol']}")
            print(f"最低阈值: {threshold} {self.chain_config['symbol']}")
            
            if native_balance_in_eth >= threshold:
                print(f"✅ {self.chain_config['symbol']}余额充足")
                return
                
            print(f"⚠️ {self.chain_config['symbol']}余额不足")
            
            # 获取USDT地址
            usdt_address = None
            for symbol, address in self.chain_config["common_tokens"].items():
                if symbol == "USDT":
                    usdt_address = address
                    break
                    
            if not usdt_address:
                print("❌ 无法找到USDT合约地址")
                return
                
            # 检查USDT余额
            usdt_balance = self.get_balance(usdt_address)
            usdt_decimals = self.get_token_decimals(usdt_address)
            usdt_balance_formatted = usdt_balance / (10 ** usdt_decimals)
            
            required_usdt = float(usdt_amount)
            print(f"\n💰 检查USDT余额...")
            print(f"当前余额: {usdt_balance_formatted} USDT")
            print(f"需要金额: {required_usdt} USDT")
            
            if usdt_balance_formatted < required_usdt:
                print(f"❌ USDT余额不足，无法自动兑换gas")
                return
                
            print(f"\n🔄 开始用{required_usdt} USDT兑换{self.chain_config['symbol']}...")
            
            # 构建兑换金额（考虑USDT的小数位数）
            usdt_amount_wei = str(int(float(usdt_amount) * (10 ** usdt_decimals)))
            
            # 执行USDT到原生代币的兑换
            swap_result = await self.swap(
                token_in=usdt_address,
                token_out=self.chain_config["native_token_address"],
                amount_in=usdt_amount_wei,
                slippage=0.5,  # 使用0.5%的滑点
                is_native_in=False,
                simulate=False
            )
            
            if swap_result["status"] == "成功":
                print(f"✅ 成功补充gas，为下次交易做准备")
            else:
                print(f"❌ 补充gas失败: {swap_result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 检查和补充gas时出错: {str(e)}")

    def _is_usdt_output(self, token_out: str) -> bool:
        """
        检查输出代币是否为USDT

        Args:
            token_out: 输出代币地址或符号

        Returns:
            bool: 是否为USDT输出
        """
        # 检查符号
        if token_out.upper() in ["USDT", "usdt"]:
            return True

        # 检查地址
        usdt_addresses = []
        for symbol, addr in self.chain_config.get("common_tokens", {}).items():
            if symbol.upper() == "USDT":
                usdt_addresses.append(addr.lower())

        return token_out.lower() in usdt_addresses

    def _extract_usdt_output(self, swap_result: dict, token_out: str) -> float:
        """
        从交易结果中提取USDT输出量

        Args:
            swap_result: 交易结果
            token_out: 输出代币地址

        Returns:
            float: USDT输出量
        """
        try:
            # 从route_summary中获取输出量
            if "route_summary" in swap_result:
                route_summary = swap_result["route_summary"]
                if "amountOut" in route_summary:
                    # 获取USDT精度（通常为6）
                    usdt_decimals = self.get_token_decimals(token_out)
                    return int(route_summary["amountOut"]) / (10 ** usdt_decimals)

            # 如果没有route_summary，尝试从其他字段获取
            if "expected_output" in swap_result and swap_result["expected_output"]:
                amounts_out = swap_result["expected_output"]
                if isinstance(amounts_out, list) and len(amounts_out) > 0:
                    usdt_decimals = self.get_token_decimals(token_out)
                    return amounts_out[-1] / (10 ** usdt_decimals)

            return 0.0

        except Exception as e:
            print(f"⚠️ 提取USDT输出量时出错: {str(e)}")
            return 0.0

    def _extract_usdt_output_from_route(self, route_summary: dict, token_out: str) -> float:
        """
        从路由摘要中提取USDT输出量

        Args:
            route_summary: 路由摘要
            token_out: 输出代币地址

        Returns:
            float: USDT输出量
        """
        try:
            if "amountOut" in route_summary:
                # 获取USDT精度（通常为6）
                usdt_decimals = self.get_token_decimals(token_out)
                return int(route_summary["amountOut"]) / (10 ** usdt_decimals)

            return 0.0

        except Exception as e:
            print(f"⚠️ 从路由摘要提取USDT输出量时出错: {str(e)}")
            return 0.0