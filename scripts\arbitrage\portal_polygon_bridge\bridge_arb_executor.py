#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Polygon和以太坊网络间套利执行器
执行验证过的套利机会
"""

import os
import sys
import json
import time
import logging
import asyncio
import threading
import argparse
from typing import Dict, Optional, Tu<PERSON>
from datetime import datetime
import queue
import traceback
from web3 import Web3
from decimal import Decimal
from src.network.tx_token_change_tracker import get_last_received_token
import yaml

# 添加项目根目录到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

# 导入KyberSwapClient
from src.dex.KyberSwap.client import KyberSwapClient
from src.dex.KyberSwap.swap import swap_tokens
from src.utils.decimal.token_decimal import get_token_decimals, token_decimal_helper

def init_directories():
    """初始化必要的目录结构"""
    base_dir = os.path.dirname(__file__)
    directories = [
        os.path.join(base_dir, "results"),
        os.path.join(base_dir, "results", "trade"),
        os.path.join(base_dir, "results", "profit"),
        os.path.join(base_dir, "results", "logs")
    ]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def manage_log_file(log_file_path):
    """管理日志文件，如果文件过大则进行轮转"""
    if os.path.exists(log_file_path):
        file_size = os.path.getsize(log_file_path)
        if file_size > 10 * 1024 * 1024:  # 10MB
            backup_path = f"{log_file_path}.{int(time.time())}"
            os.rename(log_file_path, backup_path)

def get_symbol_log_file(symbol):
    """获取代币的日志文件路径"""
    log_dir = os.path.join(os.path.dirname(__file__), "results", "logs")
    os.makedirs(log_dir, exist_ok=True)
    return os.path.join(log_dir, f"{symbol}.log")

def get_trade_log_file(symbol):
    """获取交易日志文件路径"""
    log_dir = os.path.join(os.path.dirname(__file__), "results", "trade")
    os.makedirs(log_dir, exist_ok=True)
    return os.path.join(log_dir, f"trade_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

def load_config() -> Dict:
    """加载配置文件"""
    # 确定项目根目录，并加载配置文件
    current_dir = os.path.dirname(__file__)
    
    # 向上查找三级，直到找到config目录
    for _ in range(5):
        config_path = os.path.join(current_dir, "config", "config.yaml")
        if os.path.exists(config_path):
            break
        current_dir = os.path.dirname(current_dir)
    
    # 如果仍然找不到，尝试从工作目录找
    if not os.path.exists(config_path):
        project_root = os.getcwd()
        while project_root and not os.path.exists(os.path.join(project_root, "config", "config.yaml")):
            project_root = os.path.dirname(project_root)
        
        config_path = os.path.join(project_root, "config", "config.yaml")
        
    # 如果仍然找不到，则使用绝对路径
    if not os.path.exists(config_path):
        config_path = "C:/Users/<USER>/CascadeProjects/cex_dex_arb_dev/config/config.yaml"
    
    # 如果仍然找不到，返回空字典
    if not os.path.exists(config_path):
        logging.error(f"找不到配置文件: {config_path}")
        return {}
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logging.error(f"加载配置文件失败: {str(e)}")
        return {}

def save_profit_record(symbol: str, usdt_input: float, usdt_received: float, eth_address: str = "", polygon_address: str = "") -> None:
    """保存利润记录"""
    profit_dir = os.path.join(os.path.dirname(__file__), "results", "profit")
    os.makedirs(profit_dir, exist_ok=True)
    
    record = {
        "symbol": symbol,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "usdt_input": usdt_input,
        "usdt_received": usdt_received,
        "profit": usdt_received - usdt_input,
        "profit_percentage": ((usdt_received - usdt_input) / usdt_input * 100) if usdt_input > 0 else 0,
        "eth_address": eth_address,
        "polygon_address": polygon_address
    }
    
    # 生成文件名，添加_new后缀
    file_path = os.path.join(profit_dir, f"profit_{symbol}_{datetime.now().strftime('%Y%m%d')}_new.json")
    
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                records = json.load(f)
        else:
            records = []
            
        records.append(record)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(records, f, indent=4, ensure_ascii=False)
            
    except Exception as e:
        logging.error(f"保存利润记录时出错: {str(e)}")
        logging.error(traceback.format_exc())

# 设置日志格式
def setup_logger(symbol: str) -> logging.Logger:
    """设置日志记录器"""
    # 创建日志目录
    log_dir = os.path.join(os.path.dirname(__file__), "results", "trade")
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建固定的日志文件名，添加_new后缀
    log_file = os.path.join(log_dir, f"trade_{symbol}_new.log")
    
    # 管理日志文件大小
    manage_log_file(log_file)
    
    # 创建日志记录器
    logger = logging.getLogger(f"trade_{symbol}")
    logger.setLevel(logging.INFO)
    
    # 如果已经有处理器，先清除
    if logger.handlers:
        logger.handlers.clear()
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

class BridgeArbExecutor:
    def __init__(self, symbol: str, chain: str, token_address: str, amount: float, bridge_direction: str = None, token_data: Dict = None, expected_usdt_out: float = None):
        """
        初始化执行器

        Args:
            symbol: 代币符号
            chain: 链名称 (ethereum 或 polygon)
            token_address: 代币地址
            amount: USDT数量
            bridge_direction: 交易方向 (ethereum_to_polygon 或 polygon_to_ethereum)，可选
            token_data: 代币数据，包含 polygon_address 和 eth_address
            expected_usdt_out: 预期卖出获取的USDT数量 (USDTOLD)
        """
        self.symbol = symbol
        self.chain = chain.lower()
        self.token_address = token_address
        self.amount = amount
        self.bridge_direction = bridge_direction
        self.token_data = token_data or {}
        self.expected_usdt_out = expected_usdt_out  # 预期的USDT输出量
        
        # 初始化目录
        init_directories()
        
        # 设置日志记录器
        self.logger = setup_logger(symbol)
        
        # 创建KyberSwap客户端
        self.client = KyberSwapClient(chain=self.chain)
        
        # 设置USDT地址
        self.eth_usdt = "******************************************"  # 以太坊USDT
        self.polygon_usdt = "******************************************"  # Polygon USDT
        
        # 获取正确的USDT地址
        self.usdt_address = self.eth_usdt if self.chain == "ethereum" else self.polygon_usdt
        
        # 初始化结果队列
        self.result_queue = queue.Queue()
        
        # 验证链和交易方向是否匹配
        if self.bridge_direction:
            expected_chain = "ethereum" if self.bridge_direction == "ethereum_to_polygon" else "polygon"
            if self.chain != expected_chain:
                self.logger.warning(f"警告: 指定的链 {self.chain} 与交易方向 {self.bridge_direction} 不匹配")
                self.logger.warning(f"根据交易方向，应该在 {expected_chain} 链上执行买入操作")
                # 自动修正链
                self.chain = expected_chain
                self.logger.info(f"已自动修正为在 {self.chain} 链上执行操作")
                # 重新创建客户端和设置USDT地址
                self.client = KyberSwapClient(chain=self.chain)
                self.usdt_address = self.eth_usdt if self.chain == "ethereum" else self.polygon_usdt

    @classmethod
    def from_opportunity(cls, opportunity: Dict) -> 'BridgeArbExecutor':
        """
        从套利机会信息创建执行器实例

        Args:
            opportunity: 套利机会信息，包含以下字段：
                - symbol: 代币符号
                - polygon_address: Polygon链上的代币地址
                - eth_address: 以太坊链上的代币地址
                - bridge_direction: 交易方向 (ethereum_to_polygon 或 polygon_to_ethereum)
                - usdt_input: USDT数量
                - token_amount: 代币数量
                - expected_usdt_out: 预期卖出获取的USDT数量 (USDTOLD)

        Returns:
            BridgeArbExecutor: 执行器实例
        """
        symbol = opportunity.get('symbol', 'Unknown')
        bridge_direction = opportunity.get('bridge_direction', '')
        # 从opportunity中获取usdt_input
        usdt_input = opportunity.get('usdt_input', 0)
        # 从opportunity中获取预期的USDT输出量
        expected_usdt_out = opportunity.get('expected_usdt_out', opportunity.get('usdt_output', 0))

        # 根据bridge_direction确定正确的链和token_address
        if bridge_direction == 'ethereum_to_polygon':
            chain = 'ethereum'
            token_address = opportunity.get('eth_address', '')
        elif bridge_direction == 'polygon_to_ethereum':
            chain = 'polygon'
            token_address = opportunity.get('polygon_address', '')
        else:
            # 如果没有bridge_direction，使用lower_chain
            chain = opportunity.get('lower_chain', '')
            if chain == 'ethereum':
                token_address = opportunity.get('eth_address', '')
            else:  # polygon
                token_address = opportunity.get('polygon_address', '')

        # 验证必要参数
        if not all([symbol, chain, token_address, usdt_input > 0]):
            raise ValueError(f"缺少必要的交易信息或金额无效: symbol={symbol}, chain={chain}, token_address={token_address}, usdt_input={usdt_input}")

        # 创建token_data字典
        token_data = {
            'symbol': symbol,
            'polygon_address': opportunity.get('polygon_address', ''),
            'eth_address': opportunity.get('eth_address', '')
        }

        return cls(
            symbol=symbol,
            chain=chain,
            token_address=token_address,
            amount=usdt_input,
            bridge_direction=bridge_direction,
            token_data=token_data,
            expected_usdt_out=expected_usdt_out
        )

    async def execute_buy(self) -> Dict:
        """
        执行买入操作
        
        Returns:
            Dict: 买入结果
        """
        start_time = time.time()
        
        try:
            self.logger.info("=" * 80)
            self.logger.info(f"开始执行 {self.symbol} 买入交易 - 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info(f"链: {self.chain}, 投入金额: {self.amount} USDT")
            self.logger.info(f"代币地址: {self.token_address}")
            
            # 创建执行记录
            execution_record = {
                'symbol': self.symbol,
                'chain': self.chain,
                'buy_chain': self.chain.lower(),
                'eth_address': self.token_address if self.chain == "ethereum" else "",
                'polygon_address': self.token_address if self.chain == "polygon" else "",
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'usdt_input': self.amount,
                'success': False,
                'amount_bought': 0,
                'tx_hash': '',
                'price': 0,
                'error': None
            }
            
            # 检查USDT输入金额是否有效
            if self.amount <= 0:
                error_msg = f"USDT输入金额必须大于0: {self.amount}"
                self.logger.error(f"{self.symbol}: {error_msg}")
                execution_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "execution_record": execution_record
                }
            
            # 验证代币地址
            if not self.token_address or not Web3.is_address(self.token_address):
                error_msg = f"无效的代币地址: {self.token_address}"
                self.logger.error(f"{self.symbol}: {error_msg}")
                execution_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "execution_record": execution_record
                }
            
            self.logger.info(f"{self.symbol}: 将在{self.chain}链上执行买入，代币地址: {self.token_address}")
            self.logger.info(f"{self.symbol}: 准备使用KyberSwap在{self.chain}上执行{self.amount}USDT买入{self.symbol}交易")
            
            # 调用swap_tokens执行交易
            swap_result = await swap_tokens(
                chain=self.chain,
                token_in="USDT",  # 使用简化的token_in
                token_out=self.token_address,
                amount=self.amount,  # 直接使用原始金额
                slippage=0.5,  # 0.5%滑点
                timeout=1200,  # 20分钟超时
                gas_multiplier=1.8,  # 增加gas限制
                save_gas=True,
                real=True  # 执行实际交易
            )
            
            if not swap_result.get("success"):
                error_msg = swap_result.get("error", "未知错误")
                self.logger.error(f"交易执行失败: {error_msg}")
                execution_record['error'] = f"交易执行失败: {error_msg}"
                return {
                    "success": False,
                    "error": error_msg,
                    "execution_record": execution_record
                }
            
            # 获取交易结果
            tx_hash = swap_result.get("tx_hash")
            
            # 获取实际买入的代币数量
            actual_amount = 0
            
            try:
                # 获取钱包地址
                config = load_config()
                if not config:
                    self.logger.error("无法加载配置文件")
                    return {
                        "success": False,
                        "error": "无法加载配置文件",
                        "execution_record": execution_record
                    }
                    
                private_key = config.get("dex", {}).get(self.chain, {}).get("wallet", {}).get("private_key", "")
                if not private_key:
                    self.logger.error(f"无法获取{self.chain}链的私钥")
                    return {
                        "success": False,
                        "error": f"无法获取{self.chain}链的私钥",
                        "execution_record": execution_record
                    }
                    
                # 使用 client 的 web3 实例
                web3 = self.client.web3
                account = web3.eth.account.from_key(private_key)
                user_address = account.address
                
                # 添加重试逻辑获取代币数量
                max_retries = 3
                retry_delay = 5  # 5秒
                last_error = None
                token_result = None
                
                for attempt in range(max_retries):
                    try:
                        if attempt > 0:
                            self.logger.info(f"{self.symbol}: 第{attempt + 1}次尝试获取代币数量...")
                        
                        # 使用tx_token_change_tracker获取代币数量
                        self.logger.info(f"{self.symbol}: 使用tx_token_change_tracker获取交易 {tx_hash} 的代币数量...")
                        
                        token_result = get_last_received_token(
                            tx_hash=tx_hash,
                            chain=self.chain,
                            user_address=user_address
                        )
                        
                        if "error" not in token_result:
                            # 保持原始字符串格式的数量，避免精度损失
                            actual_amount_str = str(token_result["amount"])
                            actual_amount = float(actual_amount_str)  # 仅用于显示和计算价格
                            self.logger.info(f"{self.symbol}: 从tx_token_change_tracker成功获取到代币数量: {actual_amount_str}")
                            break
                        else:
                            last_error = token_result["error"]
                            self.logger.warning(f"{self.symbol}: 第{attempt + 1}次获取代币数量失败: {last_error}")
                            if attempt < max_retries - 1:
                                self.logger.info(f"{self.symbol}: 等待{retry_delay}秒后重试...")
                                await asyncio.sleep(retry_delay)
                            continue
                            
                    except Exception as e:
                        last_error = str(e)
                        self.logger.warning(f"{self.symbol}: 第{attempt + 1}次获取代币数量出错: {last_error}")
                        if attempt < max_retries - 1:
                            self.logger.info(f"{self.symbol}: 等待{retry_delay}秒后重试...")
                            await asyncio.sleep(retry_delay)
                        continue
                
                if "error" in token_result:
                    self.logger.error(f"{self.symbol}: 在{max_retries}次尝试后仍然无法获取代币数量: {last_error}")
                    execution_record['error'] = f"无法获取代币数量: {last_error}"
                    return {
                        "success": False,
                        "error": f"无法获取代币数量: {last_error}",
                        "execution_record": execution_record
                    }
                
                # 更新执行记录
                execution_record.update({
                    'success': True,
                    'amount_bought': actual_amount_str,  # 使用字符串格式保存
                    'tx_hash': tx_hash,
                    'price': float(self.amount) / float(actual_amount_str) if float(actual_amount_str) > 0 else 0,
                    'token_symbol': token_result.get('symbol', ''),
                    'token_name': token_result.get('name', ''),
                    'token_decimals': token_result.get('decimals', 18)
                })
                
                self.logger.info("交易执行成功:")
                self.logger.info(f"  交易哈希: {tx_hash}")
                self.logger.info(f"  实际输出数量: {actual_amount} {self.symbol}")
                
                # 如果指定了桥接方向，执行桥接操作
                if self.bridge_direction:
                    self.logger.info(f"开始执行桥接操作，方向: {self.bridge_direction}")
                    bridge_result = await self.bridge_tokens(actual_amount_str)
                    
                    if not bridge_result.get("success"):
                        error_msg = bridge_result.get("error", "未知错误")
                        self.logger.error(f"桥接操作失败: {error_msg}")
                        execution_record['bridge_error'] = error_msg
                    else:
                        self.logger.info("桥接操作成功完成")
                        execution_record['bridge_success'] = True
                        if self.bridge_direction == "ethereum_to_polygon":
                            execution_record['bridge_tx'] = bridge_result['bridge_record'].get('bridge_tx')
                            execution_record['polygon_tx'] = bridge_result['bridge_record'].get('polygon_tx')
                        else:
                            execution_record['burn_tx'] = bridge_result['bridge_record'].get('burn_tx_hash')
                            execution_record['claim_tx'] = bridge_result['bridge_record'].get('claim_tx_hash')
                        
                        # 桥接成功后执行卖出操作
                        self.logger.info("开始执行卖出操作...")
                        sell_result = await self.execute_sell(actual_amount_str)
                        
                        if not sell_result.get("success"):
                            error_msg = sell_result.get("error", "未知错误")
                            self.logger.error(f"卖出操作失败: {error_msg}")
                            execution_record['sell_error'] = error_msg
                        else:
                            self.logger.info("卖出操作成功完成")
                            execution_record['sell_success'] = True
                            execution_record['sell_tx'] = sell_result.get('tx_hash')
                            execution_record['usdt_received'] = sell_result.get('usdt_received')
                
                # 记录执行时间
                execution_time = time.time() - start_time
                self.logger.info(f"交易执行完成，耗时: {execution_time:.2f}秒")
                self.logger.info("=" * 80)
                
                # 保存执行记录
                save_executed_trade(execution_record, self.symbol)
                
                return {
                    "success": True,
                    "tx_hash": tx_hash,
                    "amount_out": actual_amount,
                    "execution_record": execution_record
                }
                
            except Exception as e:
                error_msg = f"获取代币数量时出错: {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
                execution_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "execution_record": execution_record
                }
            
        except Exception as e:
            error_msg = f"执行买入操作时出错: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            
            if 'execution_record' in locals():
                execution_record['error'] = error_msg
            else:
                execution_record = {
                    'symbol': self.symbol,
                    'chain': self.chain,
                    'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'usdt_input': self.amount,
                    'success': False,
                    'error': error_msg
                }
            
            return {
                "success": False,
                "error": error_msg,
                "execution_record": execution_record
            }

    def execute_in_thread(self, result_queue: queue.Queue) -> None:
        """在新线程中执行买入操作"""
        try:
            # 检查当前线程是否已经有事件循环
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                # 如果没有事件循环，创建一个新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            try:
                # 执行买入操作
                result = loop.run_until_complete(self.execute_buy())
                
                # 将结果放入队列
                result_queue.put(result)
                
            except Exception as e:
                self.logger.error(f"执行买入操作时出错: {str(e)}")
                self.logger.error(traceback.format_exc())
                result_queue.put({
                    "success": False,
                    "error": f"执行买入操作时出错: {str(e)}"
                })
                
        except Exception as e:
            self.logger.error(f"执行线程出错: {str(e)}")
            self.logger.error(traceback.format_exc())
            # 将错误结果放入队列
            result_queue.put({
                "success": False,
                "error": f"执行线程出错: {str(e)}"
            })

    async def bridge_tokens(self, token_amount: str) -> Dict:
        """
        执行代币桥接操作
        
        Args:
            token_amount: 要桥接的代币数量
            
        Returns:
            Dict: 桥接结果
        """
        try:
            self.logger.info("=" * 80)
            self.logger.info(f"开始执行 {self.symbol} 桥接操作 - 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.logger.info(f"桥接方向: {self.bridge_direction}")
            self.logger.info(f"代币数量: {token_amount} {self.symbol}")
            
            # 创建桥接记录
            bridge_record = {
                'symbol': self.symbol,
                'bridge_direction': self.bridge_direction,
                'token_amount': token_amount,
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'success': False,
                'error': None
            }
            
            # 导入AutoBridge
            from src.bridge.pol_bridge.auto_bridge import AutoBridge
            
            # 创建AutoBridge实例
            bridge = AutoBridge()
            
            # 根据桥接方向执行不同的操作
            if self.bridge_direction == "ethereum_to_polygon":
                self.logger.info(f"从以太坊桥接到Polygon: {token_amount} {self.symbol}")
                # 使用 eth_to_polygon 方法
                result = await bridge.eth_to_polygon(
                    token_address=self.token_data["eth_address"],
                    amount=token_amount
                )
            elif self.bridge_direction == "polygon_to_ethereum":
                self.logger.info(f"从Polygon桥接到以太坊: {token_amount} {self.symbol}")
                # 使用 polygon_to_eth 方法
                result = await bridge.polygon_to_eth(
                    token_address=self.token_data["polygon_address"],
                    amount=token_amount
                )
            else:
                error_msg = f"无效的桥接方向: {self.bridge_direction}"
                self.logger.error(error_msg)
                bridge_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "bridge_record": bridge_record
                }
            
            # 处理桥接结果
            if result.get("success"):
                self.logger.info("桥接操作成功完成")
                if self.bridge_direction == "ethereum_to_polygon":
                    self.logger.info(f"桥接交易哈希: {result.get('bridge_tx')}")
                    self.logger.info(f"Polygon交易哈希: {result.get('polygon_tx')}")
                    bridge_record.update({
                        'success': True,
                        'bridge_tx': result.get('bridge_tx'),
                        'polygon_tx': result.get('polygon_tx')
                    })
                else:
                    self.logger.info(f"Burn交易哈希: {result.get('burn_tx_hash')}")
                    self.logger.info(f"Claim交易哈希: {result.get('claim_tx_hash')}")
                    bridge_record.update({
                        'success': True,
                        'burn_tx_hash': result.get('burn_tx_hash'),
                        'claim_tx_hash': result.get('claim_tx_hash')
                    })
                
                # 保存桥接记录
                self.save_bridge_record(bridge_record)
                
                return {
                    "success": True,
                    "bridge_record": bridge_record
                }
            else:
                error_msg = result.get("error", "未知错误")
                self.logger.error(f"桥接操作失败: {error_msg}")
                bridge_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "bridge_record": bridge_record
                }
                
        except Exception as e:
            error_msg = f"执行桥接操作时出错: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            
            if 'bridge_record' in locals():
                bridge_record['error'] = error_msg
            else:
                bridge_record = {
                    'symbol': self.symbol,
                    'bridge_direction': self.bridge_direction,
                    'token_amount': token_amount,
                    'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'success': False,
                    'error': error_msg
                }
            
            return {
                "success": False,
                "error": error_msg,
                "bridge_record": bridge_record
            }
    
    def save_bridge_record(self, record: dict) -> None:
        """
        保存桥接记录
        
        Args:
            record: 桥接记录
        """
        try:
            # 创建桥接记录目录
            bridge_dir = os.path.join(os.path.dirname(__file__), "results", "bridge")
            os.makedirs(bridge_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.symbol}_{timestamp}_bridge.json"
            filepath = os.path.join(bridge_dir, filename)
            
            # 保存记录
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(record, f, indent=4, ensure_ascii=False)
                
            self.logger.info(f"桥接记录已保存: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存桥接记录时出错: {str(e)}")
            self.logger.error(traceback.format_exc())

    async def execute_sell(self, token_amount: str) -> Dict:
        """
        在目标链上执行代币卖出操作

        Args:
            token_amount: 要卖出的代币数量

        Returns:
            Dict: 卖出结果
        """
        try:
            self.logger.info("=" * 80)
            self.logger.info(f"开始执行 {self.symbol} 卖出交易 - 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 确定目标链和代币地址
            target_chain = "polygon" if self.bridge_direction == "ethereum_to_polygon" else "ethereum"

            # 获取配置
            config = load_config()
            if not config:
                error_msg = "无法加载配置文件"
                self.logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg
                }

            # 获取私钥
            private_key = config.get("dex", {}).get(target_chain, {}).get("wallet", {}).get("private_key", "")
            if not private_key:
                error_msg = f"无法获取{target_chain}链的私钥"
                self.logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg
                }

            # 直接使用 token_data 中的地址
            target_token_address = None
            if self.bridge_direction == "ethereum_to_polygon":
                # 从以太坊到Polygon，使用Polygon地址
                target_token_address = self.token_data.get("polygon_address")
            else:
                # 从Polygon到以太坊，使用以太坊地址
                target_token_address = self.token_data.get("eth_address")

            if not target_token_address:
                error_msg = f"无法获取目标链上的代币地址: {self.symbol}"
                self.logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg
                }

            self.logger.info(f"目标链: {target_chain}")
            self.logger.info(f"目标链代币地址: {target_token_address}")

            # 创建卖出记录
            sell_record = {
                'symbol': self.symbol,
                'chain': target_chain,
                'token_address': target_token_address,
                'token_amount': token_amount,
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'success': False,
                'error': None,
                'expected_usdt_out': self.expected_usdt_out  # 记录预期输出
            }

            self.logger.info(f"在{target_chain}链上执行卖出操作")
            self.logger.info(f"代币地址: {target_token_address}")
            self.logger.info(f"卖出数量: {token_amount} {self.symbol}")
            if self.expected_usdt_out:
                self.logger.info(f"预期USDT输出: {self.expected_usdt_out} USDT")

            # 创建目标链的KyberSwap客户端
            target_client = KyberSwapClient(chain=target_chain)

            # 获取目标链的USDT地址
            target_usdt = self.polygon_usdt if target_chain == "polygon" else self.eth_usdt

            # 调用swap_tokens执行卖出交易（包含预验证逻辑）
            if self.expected_usdt_out and self.expected_usdt_out > 0:
                self.logger.info(f"执行卖出交易，预期USDT输出: {self.expected_usdt_out}")
            else:
                self.logger.info("执行卖出交易（无预期输出验证）")

            swap_result = await swap_tokens(
                chain=target_chain,
                token_in=target_token_address,
                token_out=target_usdt,
                amount=float(token_amount),
                slippage=0.5,
                timeout=1200,
                gas_multiplier=1.8,
                save_gas=True,
                real=True,  # 执行实际交易
                expected_usdt_out=self.expected_usdt_out  # 传递预期USDT输出用于预验证
            )
            
            if not swap_result.get("success"):
                error_msg = swap_result.get("error", "未知错误")
                status = swap_result.get("status", "失败")

                # 检查是否为预验证失败
                if status in ["预验证失败", "输出不足", "预验证异常"]:
                    self.logger.warning(f"卖出预验证失败: {error_msg}")

                    # 如果有预验证相关数据，添加到记录中
                    if "predicted_usdt_out" in swap_result:
                        sell_record['predicted_usdt_out'] = swap_result['predicted_usdt_out']
                    if "min_acceptable_usdt" in swap_result:
                        sell_record['min_acceptable_usdt'] = swap_result['min_acceptable_usdt']
                    if "expected_usdt_out" in swap_result:
                        sell_record['expected_usdt_out'] = swap_result['expected_usdt_out']

                    sell_record['error'] = error_msg
                    sell_record['status'] = status

                    return {
                        "success": False,
                        "error": error_msg,
                        "status": status,
                        "predicted_usdt_out": swap_result.get("predicted_usdt_out"),
                        "sell_record": sell_record
                    }
                else:
                    self.logger.error(f"卖出交易执行失败: {error_msg}")
                    sell_record['error'] = error_msg
                    return {
                        "success": False,
                        "error": error_msg,
                        "sell_record": sell_record
                    }
            
            # 获取交易结果
            tx_hash = swap_result.get("tx_hash")
            
            # 获取实际卖出的USDT数量
            try:
                # 使用目标链的web3实例获取钱包地址
                web3 = target_client.web3
                account = web3.eth.account.from_key(private_key)
                user_address = account.address
                
                # 使用tx_token_change_tracker获取USDT数量
                self.logger.info(f"使用tx_token_change_tracker获取交易 {tx_hash} 的USDT数量...")
                
                token_result = get_last_received_token(
                    tx_hash=tx_hash,
                    chain=target_chain,
                    user_address=user_address  # 使用钱包地址而不是代币地址
                )
                
                if "error" in token_result:
                    error_msg = token_result["error"]
                    self.logger.error(f"获取USDT数量失败: {error_msg}")
                    sell_record['error'] = error_msg
                    return {
                        "success": False,
                        "error": error_msg,
                        "sell_record": sell_record
                    }
                
                # 获取实际收到的USDT数量
                usdt_amount_str = str(token_result["amount"])
                self.logger.info(f"成功获取到USDT数量: {usdt_amount_str}")
                
                # 更新卖出记录
                sell_record.update({
                    'success': True,
                    'tx_hash': tx_hash,
                    'usdt_received': usdt_amount_str,
                    'price': float(usdt_amount_str) / float(token_amount) if float(token_amount) > 0 else 0
                })
                
                self.logger.info("卖出交易执行成功:")
                self.logger.info(f"  交易哈希: {tx_hash}")
                self.logger.info(f"  实际收到USDT数量: {usdt_amount_str}")
                
                # 保存卖出记录
                self.save_sell_record(sell_record)
                
                return {
                    "success": True,
                    "tx_hash": tx_hash,
                    "usdt_received": usdt_amount_str,
                    "sell_record": sell_record
                }
                
            except Exception as e:
                error_msg = f"获取USDT数量时出错: {str(e)}"
                self.logger.error(error_msg)
                self.logger.error(traceback.format_exc())
                sell_record['error'] = error_msg
                return {
                    "success": False,
                    "error": error_msg,
                    "sell_record": sell_record
                }
            
        except Exception as e:
            error_msg = f"执行卖出操作时出错: {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(traceback.format_exc())
            
            if 'sell_record' in locals():
                sell_record['error'] = error_msg
            else:
                sell_record = {
                    'symbol': self.symbol,
                    'chain': target_chain,
                    'token_address': target_token_address,
                    'token_amount': token_amount,
                    'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'success': False,
                    'error': error_msg
                }
            
            return {
                "success": False,
                "error": error_msg,
                "sell_record": sell_record
            }
    
    def save_sell_record(self, record: dict) -> None:
        """
        保存卖出记录
        
        Args:
            record: 卖出记录
        """
        try:
            # 创建卖出记录目录
            sell_dir = os.path.join(os.path.dirname(__file__), "results", "sell")
            os.makedirs(sell_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.symbol}_{timestamp}_sell.json"
            filepath = os.path.join(sell_dir, filename)
            
            # 保存记录
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(record, f, indent=4, ensure_ascii=False)
                
            self.logger.info(f"卖出记录已保存: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存卖出记录时出错: {str(e)}")
            self.logger.error(traceback.format_exc())

def save_executed_trade(record: dict, symbol: str) -> None:
    """
    保存已执行的交易记录
    
    Args:
        record: 交易记录
        symbol: 代币符号
    """
    try:
        # 创建交易记录目录
        trade_dir = os.path.join(os.path.dirname(__file__), "results", "trade")
        os.makedirs(trade_dir, exist_ok=True)
        
        # 生成文件名，添加_new后缀
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{symbol}_{timestamp}_new.json"
        filepath = os.path.join(trade_dir, filename)
        
        # 保存记录
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(record, f, indent=4, ensure_ascii=False)
            
        logging.info(f"交易记录已保存: {filepath}")
        
    except Exception as e:
        logging.error(f"保存交易记录时出错: {str(e)}")
        logging.error(traceback.format_exc())

def execute_trade_in_thread(opportunity: Dict) -> None:
    """
    在新线程中执行交易，不阻塞主线程
    
    Args:
        opportunity: 套利机会信息，包含以下字段：
            - symbol: 代币符号
            - polygon_address: Polygon链上的代币地址
            - eth_address: 以太坊链上的代币地址
            - bridge_direction: 交易方向 (ethereum_to_polygon 或 polygon_to_ethereum)
            - optimal_usdt: USDT数量
            - token_amount: 代币数量
    """
    try:
        # 从套利机会信息创建执行器实例
        executor = BridgeArbExecutor.from_opportunity(opportunity)
        
        # 创建结果队列
        result_queue = queue.Queue()
        
        # 创建并启动新线程
        thread = threading.Thread(
            target=executor.execute_in_thread,
            args=(result_queue,),
            name=f"Trade_{executor.symbol}"
        )
        thread.daemon = False  # 设置为非守护线程，这样主程序退出后线程仍会继续运行
        thread.start()
        
        # 不等待线程完成，直接返回
        # 线程会在后台继续执行交易
        logging.info(f"交易 {executor.symbol} 已在后台启动，继续执行下一轮任务")
            
    except Exception as e:
        error_msg = f"启动交易线程时出错: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        # 不抛出异常，让主程序继续运行
        logging.error("继续执行下一轮任务")

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="执行跨链套利交易")
    parser.add_argument("--symbol", required=True, help="代币符号")
    parser.add_argument("--chain", required=True, choices=['ethereum', 'polygon'], help="链名称")
    parser.add_argument("--amount", type=float, required=True, help="USDT数量")
    parser.add_argument("--token-address", required=True, help="代币地址")
    parser.add_argument("--bridge-direction", choices=['ethereum_to_polygon', 'polygon_to_ethereum'], help="交易方向")
    args = parser.parse_args()
    
    try:
        # 创建执行器实例
        executor = BridgeArbExecutor(
            symbol=args.symbol,
            chain=args.chain,
            token_address=args.token_address,
            amount=args.amount,
            bridge_direction=args.bridge_direction
        )
        
        # 创建并启动执行线程
        thread = threading.Thread(
            target=executor.execute_in_thread,
            name=f"Trade_{args.symbol}"
        )
        thread.start()
        
        # 等待线程完成
        thread.join()
        
        # 获取结果
        try:
            result = executor.result_queue.get(timeout=120)  # 等待最多2分钟
            if result.get("success"):
                executor.logger.info("交易执行成功")
                executor.logger.info(f"输出数量: {result['amount_out']} {args.symbol}")
            else:
                executor.logger.error(f"交易执行失败: {result.get('error', '未知错误')}")
        except queue.Empty:
            executor.logger.error("等待交易结果超时")
            
    except Exception as e:
        logging.error(f"执行交易时出错: {str(e)}")
        logging.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
